import type { DictItem } from '@/services/api/dict'
import dayjs from 'dayjs'
import { onMounted, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { z } from 'zod'
import { useListRefresh } from '@/composables/useListRefresh'
import { usePermissions } from '@/composables/usePermissions'
import { INFINITE } from '@/constants/customer'
import { Permissions } from '@/constants/permissions'
import { BillingPeriodCustomType, BillingPeriodType, PlanEndDateType, RecurringPricingModel, RecurringTieredPaymentMethod, ScheduleType, UnitBasedModelUnitType, UnitBasedPricingModel } from '@/constants/plan'
import { customer as customerApi, integrations as integrationsApi, plan as planApi } from '@/services/api'
import { useDictStore } from '@/store/modules/dict'
import { useUserStore } from '@/store/modules/user'

interface MoreRecurringTieredPricingItem {
  firstUnit: number
  lastUnit: string | typeof INFINITE
  perUnit: number
  flatFee: number
}

export interface PlanSubmitData extends Partial<Plan.Info> {

  units?: number | null

  recurringPricingModel: number | null
  recurringAmount: string | null
  recurringCurrency: string | null
  recurringBillingPeriod: number | null
  recurringBillingPeriodCustom?: number | null
  recurringBillingPeriodCustomUnit?: BillingPeriodCustomType | null

  unitBasedModel: number | null
  unitBasedModelType: string
  unitBasedModelTypeCustom: string
  unitBasedTieredModelType: string
  unitBasedTieredModelTypeCustom: string
  unitBasedAmount: string | null
  unitBasedCurrency: string | null
  unitBasedBillingPeriod: number | null
  unitBasedBillingPeriodCustom?: number | null
  unitBasedBillingPeriodCustomUnit?: BillingPeriodCustomType | null
  unitBasedIncrement: number | null
  unitBasedTieredPricing: MoreRecurringTieredPricingItem[]
  unitBasedTieredCurrency: string | null
  unitBasedTieredCurrencyPaymentMethod: number | null

  oneOffAmount: string | null
  oneOffCurrency: string | null
  oneOffPricingModel?: number | null
}

// 定义分层定价错误类型
interface TieredPricingItemError {
  firstUnit?: string
  lastUnit?: string
  perUnit?: string
  flatFee?: string
}

// 定义表单错误类型
interface FormErrors {
  plan_name: string
  amount: string
  schedule_type: string
  description: string
  currency: string
  process_type: string
  end_date: string
  end_terms: string
  is_inclusive_gst: string
  billing_period: string
  billing_period_custom: string
  pricing_model: string
  billing_period_custom_unit: string
  recurringPricingModel?: string
  recurringAmount?: string
  recurringCurrency?: string
  recurringBillingPeriod?: string
  recurringBillingPeriodCustomUnit?: string
  recurringBillingPeriodCustom?: string

  unitBasedModel?: string
  unitBasedModelType?: string
  unitBasedModelTypeCustom?: string
  unitBasedAmount?: string
  unitBasedCurrency?: string
  unitBasedIncrement?: string
  unitBasedBillingPeriod?: string
  unitBasedBillingPeriodCustom?: string
  unitBasedBillingPeriodCustomUnit?: string
  unitBasedTieredPricing?: string | Record<number, TieredPricingItemError>
  unitBasedTieredCurrency?: string
  unitBasedTieredCurrencyPaymentMethod?: string

  oneOffAmount?: string
  oneOffCurrency?: string
  oneOffPricingModel?: string

  invoice_theme_id?: string
  invoice_account_code?: string
}

export function apiTransform(data: Plan.Info, formData: PlanSubmitData): PlanSubmitData {
  // 更新表单数据
  Object.assign(formData, {
    plan_id: data.plan_id,
    description: data.description,
    plan_name: data.plan_name,
    is_inclusive_gst: data.is_inclusive_gst,
    schedule_type: data.schedule_type,
    end_date: data.end_date ? new Date(data.end_date) : new Date(),
    is_surcharge: data.is_surcharge,
    end_date_type: data.end_date_type,
    end_terms: data.end_terms,
    status: data.status,
    surcharge_rate: data.surcharge_rate,
    units: data.units,
    start_date: data.start_date ? dayjs(data.start_date).toDate() : null,
    invoice_theme_id: data?.invoice_theme_id || '',
    invoice_account_code: data?.invoice_account_code || '',
  })

  if (data.schedule_type === ScheduleType.OneOff) {
    Reflect.set(formData, 'oneOffAmount', Number(data.prices[0].amount_per_unit))
    Reflect.set(formData, 'oneOffCurrency', data.prices[0].currency)
    Reflect.set(formData, 'oneOffPricingModel', data.pricing_model)
  }
  if (data.schedule_type === ScheduleType.Recurring) {
    Reflect.set(formData, 'recurringPricingModel', data.pricing_model)
    Reflect.set(formData, 'recurringBillingPeriod', data.process_type)
    if (formData.recurringBillingPeriod === BillingPeriodType.Custom) {
      Reflect.set(formData, 'recurringBillingPeriodCustom', data.custom_cycle)
      Reflect.set(formData, 'recurringBillingPeriodCustomUnit', data.custom_cycle_type)
    }
    Reflect.set(formData, 'recurringAmount', data.prices[0].amount_per_unit)
    Reflect.set(formData, 'recurringCurrency', data.prices[0].currency)
  }
  if (data.schedule_type === ScheduleType.UnitBased) {
    Reflect.set(formData, 'unitBasedModel', data.pricing_model)
    Reflect.set(formData, 'unitBasedBillingPeriod', data.process_type)
    if (formData.unitBasedBillingPeriod === BillingPeriodType.Custom) {
      Reflect.set(formData, 'unitBasedBillingPeriodCustom', data.custom_cycle)
      Reflect.set(formData, 'unitBasedBillingPeriodCustomUnit', data.custom_cycle_type)
    }

    if (formData.unitBasedModel === UnitBasedPricingModel.StandardPricing) {
      if (['Unit', 'Hours', 'User'].includes(data.prices[0].unit_type as string)) {
        Reflect.set(formData, 'unitBasedModelType', data.prices[0].unit_type)
      }
      else {
        Reflect.set(formData, 'unitBasedModelType', UnitBasedModelUnitType.Custom)
        Reflect.set(formData, 'unitBasedModelTypeCustom', data.prices[0].unit_type)
      }
      Reflect.set(formData, 'unitBasedAmount', data.prices[0].amount_per_unit)
      Reflect.set(formData, 'unitBasedCurrency', data.prices[0].currency)
      Reflect.set(formData, 'unitBasedIncrement', Number(data.prices[0].units))
    }

    if (formData.unitBasedModel === UnitBasedPricingModel.TieredPricing) {
      if (['Unit', 'Hours', 'User'].includes(data.prices[0].unit_type as string)) {
        Reflect.set(formData, 'unitBasedTieredModelType', data.prices[0].unit_type)
      }
      else {
        Reflect.set(formData, 'unitBasedTieredModelType', UnitBasedModelUnitType.Custom)
        Reflect.set(formData, 'unitBasedTieredModelTypeCustom', data.prices[0].unit_type)
      }
      Reflect.set(formData, 'unitBasedTieredCurrency', data.prices[0].currency)
      Reflect.set(formData, 'unitBasedTieredPricing', data.prices.map((item) => {
        return {
          firstUnit: Number(item.first_unit),
          lastUnit: item.last_unit === null ? INFINITE : item.last_unit,
          perUnit: Number(item.amount_per_unit),
          flatFee: Number(item.amount_flat_fee),
        }
      }))
    }
  }
  return formData
}

export function usePlanForm(mode: 'add' | 'edit' | 'view' | 'customerView', planId?: string) {
  const { user, activeBid } = useUserStore()
  const { hasPermission } = usePermissions()
  const { t } = useI18n()
  const { getDictByType, isTypeLoading } = useDictStore()
  const { backWithRefresh } = useListRefresh('planSubscriptionList', () => { })
  const customerId = ref('')

  // 加载和提交状态
  const loading = ref(false)
  const submitting = ref(false)

  // 字典
  const options = ref<Record<string, DictItem[]>>({
    status: [],
    process_type: [],
    currency: [],
    billing_period: [],
    billing_period_custom_unit: [],
    recurring_pricing_model: [
      { label: 'Standard Pricing', value: RecurringPricingModel.StandardPricing },
    ],
    unit_based_pricing_model: [
      { label: 'Standard Pricing', value: UnitBasedPricingModel.StandardPricing },
      { label: 'Tiered pricing', value: UnitBasedPricingModel.TieredPricing },
    ],
    unit_based_model_type: [
      { label: 'Unit', value: UnitBasedModelUnitType.Unit },
      { label: 'Hours', value: UnitBasedModelUnitType.Hours },
      { label: 'User', value: UnitBasedModelUnitType.User },
      { label: 'Custom', value: UnitBasedModelUnitType.Custom },
    ],
    one_off_pricing_model: [
      { label: 'Flat', value: RecurringPricingModel.Flat },
    ],
    unit_based_tiered_payment_method: [
      { label: 'Volume', value: RecurringTieredPaymentMethod.Volume },
      { label: 'Graduated', value: RecurringTieredPaymentMethod.Graduated },
    ],
    endDateType: [
      { label: 'Specify by end date', value: PlanEndDateType.SpecifyByEndDate },
      { label: 'Specify by term', value: PlanEndDateType.SpecifyByTerm },
      { label: 'Good till cancel', value: PlanEndDateType.GoodTillCancel },
    ],
    // xero Invoice Template
    invoiceTemplate: [],
    // xero chart of account
    invoiceChartOfAccount: [],
  })

  // 表单数据
  const formData = reactive<PlanSubmitData>({
    plan_name: ``,
    amount: 0,
    schedule_type: ScheduleType.Recurring,
    process_type: 1,
    description: '',
    end_date: new Date(),
    is_inclusive_gst: false,
    is_surcharge: false,
    plan_id: '',
    status: null,
    currency: null,
    billing_period: null,
    billing_period_custom: null,
    billing_period_custom_unit: null,
    pricing_model: null,
    end_date_type: PlanEndDateType.SpecifyByEndDate,
    end_terms: 1,

    // more recurring
    recurringPricingModel: RecurringPricingModel.StandardPricing,
    recurringAmount: null,
    recurringCurrency: null,
    recurringBillingPeriod: BillingPeriodType.Monthly,
    recurringBillingPeriodCustom: 1,
    recurringBillingPeriodCustomUnit: BillingPeriodCustomType.Week,

    // more unit based
    unitBasedModel: RecurringPricingModel.StandardPricing,
    unitBasedModelType: UnitBasedModelUnitType.Unit,
    unitBasedModelTypeCustom: '',
    unitBasedTieredModelType: UnitBasedModelUnitType.Unit,
    unitBasedTieredModelTypeCustom: '',
    unitBasedAmount: null,
    unitBasedCurrency: null,
    unitBasedIncrement: null,
    unitBasedBillingPeriod: BillingPeriodType.Monthly,
    unitBasedBillingPeriodCustom: 1,
    unitBasedBillingPeriodCustomUnit: BillingPeriodCustomType.Week,
    unitBasedTieredPricing: [
      { firstUnit: 1, lastUnit: '2', perUnit: 0, flatFee: 0 },
      { firstUnit: 3, lastUnit: INFINITE, perUnit: 0, flatFee: 0 },
    ],
    unitBasedTieredCurrency: null,
    unitBasedTieredCurrencyPaymentMethod: RecurringTieredPaymentMethod.Volume,

    // more one off
    oneOffAmount: null,
    oneOffCurrency: null,
    oneOffPricingModel: RecurringPricingModel.Flat,

    invoice_theme_id: '',
    invoice_account_code: '',
  })

  // 表单错误
  const formErrors = reactive<FormErrors>({
    plan_name: '',
    amount: '',
    schedule_type: '',
    description: '',
    currency: '',
    process_type: '',
    end_date: '',
    is_inclusive_gst: '',
    billing_period: '',
    billing_period_custom: '',
    pricing_model: '',
    billing_period_custom_unit: '',
    recurringBillingPeriodCustom: '',
    end_terms: '',

    invoice_theme_id: '',
    invoice_account_code: '',
  })

  // 定义通用的货币验证schema
  const currencySchema = z.string({ message: t('validation.required') }).nullable()
  const amountSchema = z.preprocess((val) => {
    if (val === '' || val === null || val === undefined) { return '' }
    if (typeof val === 'number') { return val }
    if (typeof val === 'string' && !Number.isNaN(Number(val))) { return Number(val) }
    return val
  }, z.union([
    z.string().min(1, { message: t('validation.required') }),
    z.number().min(0.01, { message: t('validation.required') }),
  ]))

  // 定义基础表单验证schema
  const baseFormSchema = z.object({
    plan_name: z.string().min(1, { message: t('validation.required') }).max(100),
    process_type: z.number().min(1, { message: t('validation.required') }),
    end_date: z.date().nullable(),
    is_inclusive_gst: z.boolean().optional(),
    description: z.string().max(500).optional(),
    schedule_type: z.number().min(1).max(3), // 更新为支持3种模式
  })

  // 定义周期性定价规则验证schema
  const recurringPricingSchema = z.object({
    end_date_type: z.number().min(1, { message: t('validation.required') }).nullable(),
    end_terms: z.number().min(1, { message: t('validation.required') }).nullable(),
    recurringPricingModel: z.number().min(1, { message: t('validation.required') }).nullable(),
    recurringAmount: amountSchema.nullable(),
    recurringCurrency: currencySchema.nullable(),
    recurringBillingPeriod: z.number().min(1, { message: t('validation.required') }).nullable(),
    recurringBillingPeriodCustom: z.number().nullable(),
    recurringBillingPeriodCustomUnit: z.number().nullable(),
  })

  // 定义按单位定价规则验证schema
  const unitBasedPricingSchema = z.object({
    end_date_type: z.number().min(1, { message: t('validation.required') }).nullable(),
    end_terms: z.number().min(0, { message: t('validation.required') }).nullable(),
    unitBasedModel: z.number().min(1, { message: t('validation.required') }).nullable(),
    unitBasedModelType: z.string().min(1, { message: t('validation.required') }),
    unitBasedModelTypeCustom: z.string().optional(),
    unitBasedAmount: amountSchema.nullable(),
    unitBasedCurrency: currencySchema.nullable(),
    unitBasedIncrement: z.number().min(0.1, { message: t('validation.required') }).nullable(),
    unitBasedBillingPeriod: z.number().min(1, { message: t('validation.required') }).nullable(),
    unitBasedBillingPeriodCustom: z.number().nullable(),
    unitBasedBillingPeriodCustomUnit: z.number().nullable(),
    unitBasedTieredModelType: z.string().min(1, { message: t('validation.required') }),
    unitBasedTieredModelTypeCustom: z.string().optional(),
    unitBasedTieredPricing: z.array(z.object({
      firstUnit: z.number().min(1),
      lastUnit: z.union([z.string(), z.number()]),
      perUnit: z.number().min(0),
      flatFee: z.number().min(0),
    })).min(1, { message: t('validation.required') }),
    unitBasedTieredCurrency: currencySchema.nullable(),
    unitBasedTieredCurrencyPaymentMethod: z.number().min(1, { message: t('validation.required') }).nullable(),
  })

  // 定义一次性定价规则验证schema
  const oneOffPricingSchema = z.object({
    oneOffAmount: amountSchema.nullable(),
    oneOffCurrency: currencySchema.nullable(),
    oneOffPricingModel: z.number().min(1, { message: t('validation.required') }).nullable(),
  })

  const invoiceSchema = z.object({
    invoice_theme_id: z.string().optional(),
    invoice_account_code: z.string().optional(),
  })

  // 组合所有验证规则
  const formSchema = baseFormSchema
    .merge(recurringPricingSchema)
    .merge(unitBasedPricingSchema) // 添加Unit-based验证规则
    .merge(oneOffPricingSchema)
    .merge(invoiceSchema)
    .superRefine((data, ctx) => {
      // 验证终止日期类型
      if (data.end_date_type === PlanEndDateType.SpecifyByEndDate) {
        if (!data.end_date) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: t('validation.required'),
            path: ['end_date'],
          })
        }
      }

      if (data.end_date_type === PlanEndDateType.SpecifyByTerm) {
        if (!data.end_terms || data.end_terms === 0) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: t('validation.required'),
            path: ['end_terms'],
          })
        }
      }

      // Recurring 模式验证
      if (data.schedule_type === ScheduleType.Recurring) {
        if (data.recurringPricingModel === RecurringPricingModel.StandardPricing) {
          if (!data.recurringAmount || !data.recurringCurrency) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: t('validation.required'),
              path: ['recurringAmount'],
            })
          }
          if (!data.recurringBillingPeriod) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: t('validation.required'),
              path: ['recurringBillingPeriod'],
            })
          }
        }

        if (data.recurringBillingPeriod === BillingPeriodType.Custom && (!data.recurringBillingPeriodCustom || !data.recurringBillingPeriodCustomUnit)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: t('validation.required'),
            path: ['recurringBillingPeriodCustom'],
          })
        }

        // recurringBillingPeriodCustom Day only 7 - 365
        if (data.recurringBillingPeriod === BillingPeriodType.Custom && data.recurringBillingPeriodCustomUnit === BillingPeriodCustomType.Day
          && ((data?.recurringBillingPeriodCustom || 0) < 7 || (data?.recurringBillingPeriodCustom || 0) > 365)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Custom billing period must be between 7 and 365 days',
            path: ['recurringBillingPeriodCustom'],
          })
        }
      }

      // Unit-based 模式验证
      if (data.schedule_type === ScheduleType.UnitBased) {
        // 验证 Unit-based 模型类型
        if (data.unitBasedModelType === UnitBasedModelUnitType.Custom && !data.unitBasedModelTypeCustom) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: t('validation.required'),
            path: ['unitBasedModelTypeCustom'],
          })
        }

        // Standard Pricing 验证
        if (data.unitBasedModel === UnitBasedPricingModel.StandardPricing) {
          if (!data.unitBasedAmount || !data.unitBasedCurrency) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: t('validation.required'),
              path: ['unitBasedAmount'],
            })
          }
          if (!data.unitBasedBillingPeriod) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: t('validation.required'),
              path: ['unitBasedBillingPeriod'],
            })
          }
          if (!data.unitBasedIncrement) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: t('validation.required'),
              path: ['unitBasedIncrement'],
            })
          }

          // 更详细地验证当选择Custom类型时，自定义字段必须有值
          if (data.unitBasedModelType === UnitBasedModelUnitType.Custom) {
            if (!data.unitBasedModelTypeCustom || data.unitBasedModelTypeCustom.trim() === '') {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: t('validation.required'),
                path: ['unitBasedModelTypeCustom'],
              })
            }
          }
        }
        // Tiered Pricing 验证
        else if (data.unitBasedModel === UnitBasedPricingModel.TieredPricing) {
          if (data.unitBasedTieredModelType === UnitBasedModelUnitType.Custom && !data.unitBasedTieredModelTypeCustom) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: t('validation.required'),
              path: ['unitBasedTieredModelTypeCustom'],
            })
          }

          // 更详细地验证当选择Custom类型时，自定义字段必须有值
          if (data.unitBasedTieredModelType === UnitBasedModelUnitType.Custom) {
            if (!data.unitBasedTieredModelTypeCustom || data.unitBasedTieredModelTypeCustom.trim() === '') {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: t('validation.required'),
                path: ['unitBasedTieredModelTypeCustom'],
              })
            }
          }

          if (!data.unitBasedTieredCurrency) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: t('validation.required'),
              path: ['unitBasedTieredCurrency'],
            })
          }
          if (!data.unitBasedBillingPeriod) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: t('validation.required'),
              path: ['unitBasedBillingPeriod'],
            })
          }

          // 验证分层定价的连续性和有效性
          data.unitBasedTieredPricing.forEach((tier, index) => {
            // 验证单价不能为负数或为空
            if (tier.perUnit === null || tier.perUnit === undefined || tier.perUnit < 0) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: t('validation.per_unit_invalid'),
                path: [`unitBasedTieredPricing.${index}.perUnit`],
              })
            }

            // 验证固定费用不能为负数或为空
            if (tier.flatFee === null || tier.flatFee === undefined || tier.flatFee < 0) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: t('validation.flat_fee_invalid'),
                path: [`unitBasedTieredPricing.${index}.flatFee`],
              })
            }

            // 验证首个单位必须为正整数
            if (!tier.firstUnit || tier.firstUnit <= 0 || !Number.isInteger(tier.firstUnit)) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: t('validation.first_unit_invalid'),
                path: [`unitBasedTieredPricing.${index}.firstUnit`],
              })
            }

            // 验证最后单位必须大于首个单位或为无限
            if (tier.lastUnit !== INFINITE
              && (typeof tier.lastUnit === 'string' ? Number(tier.lastUnit) : tier.lastUnit) < tier.firstUnit) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: t('validation.last_unit_invalid'),
                path: [`unitBasedTieredPricing.${index}.lastUnit`],
              })
            }

            // 验证与前一层级是否有重叠
            if (index > 0) {
              const prevTier = data.unitBasedTieredPricing[index - 1]
              const prevLastUnit = typeof prevTier.lastUnit === 'string'
                ? (prevTier.lastUnit === INFINITE ? Infinity : Number(prevTier.lastUnit))
                : prevTier.lastUnit

              if (tier.firstUnit <= prevLastUnit) {
                ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: t('validation.tiered_pricing_overlap'),
                  path: [`unitBasedTieredPricing.${index}.firstUnit`],
                })
              }

              // 验证是否有空隙（当前层级的首个单位应该正好是前一层级的最后单位+1）
              if (prevLastUnit !== Infinity && tier.firstUnit > prevLastUnit + 1) {
                ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: t('validation.tiered_pricing_gap'),
                  path: [`unitBasedTieredPricing.${index}.firstUnit`],
                })
              }
            }
            else {
              // 第一层级的首个单位必须从1开始
              if (tier.firstUnit !== 1) {
                ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: t('validation.first_tier_must_start_with_one'),
                  path: [`unitBasedTieredPricing.${index}.firstUnit`],
                })
              }
            }
          })
        }

        // 验证自定义计费周期
        if (data.unitBasedBillingPeriod === BillingPeriodType.Custom
          && (!data.unitBasedBillingPeriodCustom || !data.unitBasedBillingPeriodCustomUnit)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: t('validation.required'),
            path: ['unitBasedBillingPeriodCustom'],
          })
        }
      }

      // 如果已经连接了 xero，那么 invoice_account_code 和 invoice_theme_id 都必须有值
      if (user?.xero_link) {
        if (data.invoice_account_code && !data.invoice_theme_id) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: t('validation.required'),
            path: ['invoice_theme_id'],
          })
        }

        if (data.invoice_theme_id && !data.invoice_account_code) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: t('validation.required'),
            path: ['invoice_account_code'],
          })
        }
      }
    })

  // 获取计划详情
  const fetchPlanDetails = async (id?: string) => {
    if ((mode === 'edit' && !planId) || (mode === 'view' && !id) || (mode === 'customerView' && !id)) { return }

    const apiMethod = mode === 'customerView' ? customerApi.getCustomerPlanDetail : planApi.getDetail

    loading.value = true
    try {
      const { code, data } = await apiMethod((planId || id) as string, customerId.value)

      if (code === 0 && data) {
        apiTransform(data, formData)
      }
    }
    finally {
      loading.value = false
    }
  }

  // 验证表单
  const validateForm = async () => {
    try {
      // 重置错误
      Object.keys(formErrors).forEach((key) => {
        formErrors[key as keyof typeof formErrors] = ''
      })

      // 验证数据
      const dataToValidate = {
        ...formData,
        // 确保 amount 不为空
        amount: formData.amount === '' || formData.amount === null || formData.amount === undefined ? '' : formData.amount,
      }

      await formSchema.parseAsync(dataToValidate)
      return true
    }
    catch (error) {
      console.log('error, ', error)
      if (error instanceof z.ZodError) {
        error.errors.forEach((err) => {
          if (err.path.length > 0) {
            const path = err.path[0] as string

            // 处理嵌套路径，如 moreRecurringTieredPricing.1.lastUnit
            if (path.includes('.')) {
              const parts = path.split('.')
              const mainPath = parts[0]

              // 处理分层定价的错误
              if (mainPath === 'unitBasedTieredPricing') {
                // 构建更明确的错误消息
                const tierIndex = parts.length >= 2 ? Number.parseInt(parts[1]) + 1 : ''
                // 设置错误消息，包含层级和字段信息
                formErrors.unitBasedTieredPricing = `${t('planSubscription.tieredPricing.tier')} ${tierIndex}: ${err.message}`
              }
              else {
                // 对于其他嵌套路径，只使用主路径
                formErrors[mainPath as keyof typeof formErrors] = err.message
              }
            }
            else {
              // 对于非嵌套路径，直接设置错误
              formErrors[path as keyof typeof formErrors] = err.message
            }
          }
        })
      }
      return false
    }
  }

  // 转换金额
  const convertAmount = (amount: number | string | null): string | number => {
    return typeof amount === 'number' && !Number.isNaN(amount) ? String(amount) : (amount || '0')
  }

  // 提交表单
  const submitForm = async (event?: Event) => {
    // 权限检查
    if (mode === 'add' && !hasPermission(Permissions.PLAN_CREATE)) {
      return
    }
    if (mode === 'edit' && !hasPermission(Permissions.PLAN_UPDATE)) {
      return
    }

    if (event) {
      event?.preventDefault()
    }
    if (await validateForm()) {
      submitting.value = true
      try {
        const submitData = {
          plan_name: formData.plan_name,
          description: formData.description,
          schedule_type: formData.schedule_type,
          is_inclusive_gst: formData.is_inclusive_gst,
          is_surcharge: formData.is_surcharge,
          end_date: dayjs(formData.end_date).format('YYYY-MM-DD HH:mm:ss'),
          end_date_type: formData.end_date_type,
          end_terms: formData.end_terms,
          prices: [],
          invoice_theme_id: formData.invoice_theme_id,
          invoice_account_code: formData.invoice_account_code,
        } as Api.PlanCreateReq

        if (formData.schedule_type === ScheduleType.OneOff) {
          submitData.pricing_model = formData.oneOffPricingModel as RecurringPricingModel
          submitData.prices.push({
            currency: formData.oneOffCurrency as string,
            amount_per_unit: convertAmount(formData.oneOffAmount) as number,
            amount_flat_fee: null,
            first_unit: 1,
            last_unit: INFINITE,
            unit_type: '',
            units: 1,
          })
        }
        else if (formData.schedule_type === ScheduleType.Recurring) {
          submitData.pricing_model = formData.recurringPricingModel as RecurringPricingModel
          submitData.process_type = formData.recurringBillingPeriod as number
          if (formData.recurringBillingPeriod === BillingPeriodType.Custom) {
            submitData.custom_cycle_type = formData.recurringBillingPeriodCustomUnit as number
            submitData.custom_cycle = formData.recurringBillingPeriodCustom as number
          }
          submitData.prices.push({
            currency: formData.recurringCurrency as string,
            amount_per_unit: convertAmount(formData.recurringAmount) as number,
            amount_flat_fee: null,
            first_unit: 1,
            last_unit: INFINITE,
            unit_type: '',
            units: 1,
          })
        }
        else if (formData.schedule_type === ScheduleType.UnitBased) {
          submitData.pricing_model = formData.unitBasedModel as UnitBasedPricingModel
          submitData.process_type = formData.unitBasedBillingPeriod as number

          if (formData.unitBasedBillingPeriod === BillingPeriodType.Custom) {
            submitData.custom_cycle_type = formData.unitBasedBillingPeriodCustomUnit as number
            submitData.custom_cycle = formData.unitBasedBillingPeriodCustom as number
          }

          switch (formData.unitBasedModel) {
            case UnitBasedPricingModel.StandardPricing:
              submitData.prices.push({
                currency: formData.unitBasedCurrency as string,
                amount_per_unit: convertAmount(formData.unitBasedAmount) as number,
                amount_flat_fee: null,
                first_unit: 1,
                last_unit: INFINITE,
                unit_type: formData.unitBasedModelType === UnitBasedModelUnitType.Custom ? formData.unitBasedModelTypeCustom : formData.unitBasedModelType,
                units: formData.unitBasedIncrement as number,
              })
              break

            case UnitBasedPricingModel.TieredPricing: {
              submitData.tiered_type = formData.unitBasedTieredCurrencyPaymentMethod as number
              submitData.prices = formData.unitBasedTieredPricing.map((item) => {
                return {
                  currency: formData.unitBasedTieredCurrency as string,
                  amount_per_unit: convertAmount(item.perUnit) as number,
                  amount_flat_fee: convertAmount(item.flatFee) as number,
                  first_unit: item.firstUnit,
                  last_unit: item.lastUnit,
                  unit_type: formData.unitBasedTieredModelType === UnitBasedModelUnitType.Custom ? formData.unitBasedTieredModelTypeCustom : formData.unitBasedTieredModelType,
                  units: 1,
                }
              })
              break
            }
          }
        }

        if (mode === 'view' || mode === 'customerView') {
          return submitData
        }

        if (mode === 'add') {
          const { code } = await planApi.create(submitData, {
            headers: {
              'Business-Id': activeBid,
            },
          })
          if (code === 0) {
            window.$toast.add({
              severity: 'success',
              summary: 'Successful',
              detail: 'Plan created successfully',
            })
            backWithRefresh()
          }
        }
        else {
          const { code } = await planApi.edit({
            ...submitData,
            status: formData.status as number,
            plan_id: planId as string,
          })
          if (code === 0) {
            window.$toast.add({
              severity: 'success',
              summary: 'Successful',
              detail: 'Plan updated successfully',
            })
            backWithRefresh()
          }
        }
      }
      catch (error) {
        console.error(`Error ${mode === 'add' ? 'creating' : 'updating'} plan:`, error)
        window.$toast.add({
          severity: 'error',
          summary: 'Error',
          detail: `An error occurred while ${mode === 'add' ? 'creating' : 'updating'} the plan`,
          life: 3000,
        })
      }
      finally {
        submitting.value = false
      }
    }
  }

  const setPlanId = (planId: string) => {
    fetchPlanDetails(planId)
  }

  const setCustomerId = (id: string) => {
    customerId.value = id
  }

  // 组件挂载时获取计划详情
  onMounted(() => {
    Promise.all([
      getDictByType('currency').then((res) => {
        options.value.currency = res
        formData.recurringCurrency = res[0].value as string
        formData.unitBasedCurrency = res[0].value as string
        formData.oneOffCurrency = res[0].value as string
        formData.unitBasedTieredCurrency = res[0].value as string
      }),
      getDictByType('plan_process_type').then((res) => {
        options.value.billing_period = res
      }),
      getDictByType('plan_tiered_type').then((res) => {
        options.value.process_type = res
      }),
      getDictByType('plan_custom_cycle_type').then((res) => {
        options.value.billing_period_custom_unit = res
      }),
    ])
    if (mode === 'edit' && planId) {
      fetchPlanDetails()
      getDictByType('plan_status').then((res) => {
        options.value.status = res
      })
    }

    if (user?.xero_link) {
      integrationsApi.getXeroChartOfAccounts().then((res) => {
        options.value.invoiceChartOfAccounts = res.data.map((item) => {
          return {
            label: item.name,
            value: item.account_code,
          }
        })
      })
      integrationsApi.getXeroInvoiceTemplates().then((res) => {
        options.value.invoiceTemplate = res.data.map((item) => {
          return {
            label: item.name || '',
            value: item.theme_id || '',
          }
        })
      })
    }
  })

  return {
    formData,
    formErrors,
    loading,
    options,
    optionsLoading: isTypeLoading,
    submitting,

    validateForm,
    submitForm,
    INFINITE,
    setPlanId,
    setCustomerId,
    customerId,
    mode,
  }
}
