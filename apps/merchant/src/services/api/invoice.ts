import { GET, POST } from '@/services/http'

/**
 * Get invoice list
 */
export const getInvoiceList = (params: Api.InvoiceListReq) => {
  return GET<CommonListRes<Invoice.Info[]>>('invoice/getInvoiceList', { params })
}

/**
 * Get invoice list
 */
export const exportInvoices = (params: Api.InvoiceListReq) => {
  return GET<CommonRes>('invoice/invoiceExport', { params })
}

/**
 * Get invoice detail
 */
export const getInvoiceDetail = (params: Api.InvoiceDetailReq) => {
  return GET<Invoice.Info>('invoice/getInvoiceDetail', { params })
}

export const sendInvoiceEmail = (data: Api.InvoiceDetailSendInvoiceEmailReq) => {
  return POST('invoice/sendInvoiceEmail', data)
}

export const getInvoiceDashboard = () => {
  return GET<Invoice.DashboardData[]>('invoice/getInvoiceStatus')
}
