import { GET, POST } from '@/services/http'

/**
 * 获取账户列表
 */
export const getAccountList = (params: Api.AccountListReq) => {
  return GET('invoice/getAccountList', { params })
}

/**
 * 获取账户详情
 */
export const getAccountDetail = (params: Api.AccountDetailReq) => {
  return GET('invoice/getAccountDetail', { params })
}

/**
 * 添加账户
 */
export const addAccount = (data: Api.AddAccountReq) => {
  return POST('invoice/addAccount', data)
}

/**
 * 更新账户
 */
export const updateAccount = (data: Api.UpdateAccountReq) => {
  return POST('invoice/updateAccount', data)
}

/**
 * 删除账户
 */
export const deleteAccount = (data: Api.DeleteAccountReq) => {
  return POST('invoice/deleteAccount', data)
}
