import { GET, POST } from '@/services/http'

/**
 * 获取联系人列表
 */
export const getList = (params: Api.ContactListReq) => {
  return GET('/invoice/getContactList', { params })
}

/**
 * 添加联系人
 */
export const create = (data: Api.CreateContactReq) => {
  return POST('/invoice/addContact', data)
}

/**
 * 更新联系人
 */
export const update = (data: Api.UpdateContactReq) => {
  return POST('/invoice/updateContact', data)
}
