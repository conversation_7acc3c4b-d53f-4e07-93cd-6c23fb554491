import type { AxiosRequestConfig } from 'axios'
import { GET, POST } from '../http'

export const xeroSubmitPayment = (data: Api.XeroSubmitPaymentReq, options?: AxiosRequestConfig) => POST<{ url: string }>('invoice/submit', data, options)

export const xeroDisconnect = (options?: AxiosRequestConfig) => GET('invoice/break', { ...options })

export const getXeroInfo = () => GET<Api.XeroInfoRes>('invoice/info')

export const refreshXeroStatus = (_: any, options?: AxiosRequestConfig) => GET('invoice/refreshAccessToken', { ...options })

export const getXeroChartOfAccounts = () => GET<Api.XeroChartOfAccountsRes[]>('invoice/getAllAccountCode')

export const getXeroInvoiceTemplates = () => GET<Api.XeroInvoiceTemplatesRes[]>('invoice/getThemeList')

export const createXeroInvoice = (data: Api.XeroCreateInvoiceReq, options?: AxiosRequestConfig) => POST<{ id: string }>('invoice/addInvoice', data, options)

export const updateXeroInvoice = (data: Api.XeroEditInvoiceReq) => POST<{ id: string }>('invoice/updateInvoice', data)

export const getXeroInvoiceConfig = (query: Api.GetXeroInvoiceConfigReq) => GET<Api.InvoiceConfigRes>('invoice/getInvoicePaymentDetail', { params: query })

export const pay = (data: Api.XeroPayReq) => POST<CommonRes<Api.XeroPayRes>>('invoice/invoicePaymentSubmit', data)

export const syncXeroData = (_: any, options?: AxiosRequestConfig) => POST<CommonRes<Api.XeroPayRes>>('invoice/syncChannelData', { ...options })

export const getXeroAccountSettings = () => GET<Api.XeroSettingsRes>('invoice/getAccountConfig')

export const updateXeroSettings = (data: { update_list: Api.XeroSettingsRes['branding_themes'] }) => POST<CommonRes<Api.XeroSettingsRes>>('invoice/updateBrandingThemeConfig', data)

export const updateXeroBankAccount = (data: { bank_account_id: number, logo: string, file_name: string }) => POST<CommonRes<Api.XeroSettingsRes>>('invoice/updateInvoiceChannelConfig', data)
