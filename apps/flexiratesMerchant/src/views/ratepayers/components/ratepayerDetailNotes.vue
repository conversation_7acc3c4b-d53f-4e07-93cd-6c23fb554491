<script setup lang="ts">
import { MDialog } from '@ui'
import { toTypedSchema } from '@vee-validate/yup'
import dayjs from 'dayjs'
import ProgressSpinner from 'primevue/progressspinner'
import { Field, Form as VForm } from 'vee-validate'
import { ref } from 'vue'
import * as yup from 'yup'
import { useRequestList } from '@/composables/useRequestList'
import { ratepayer as ratepayerApi } from '@/services/api'

const props = withDefaults(defineProps<{
  customerId?: string | number | undefined
}>(), {
  customerId: '',
})

// 使用 useRequestList 处理 notes 数据
const {
  list: notesList,
  refresh,
  loading: isListLoading,
} = useRequestList<Ratepayer.Note, Api.RatepayerNotesReq>({
  requestFn: ratepayerApi.getRatepayerNotes,
  defaultParams: {
    customer_id: props.customerId?.toString() || '',
    page: 1,
    page_size: 100,
  },
})

const isAddNoteModalVisible = ref(false)
const isSubmitting = ref(false)

const addNoteSchema = toTypedSchema(yup.object({
  title: yup.string().required('Title is required'),
}))

const openAddNoteModal = () => {
  isAddNoteModalVisible.value = true
}

const handleAddNote = async (values: any) => {
  isSubmitting.value = true
  try {
    const { code } = await ratepayerApi.createRatepayerNote({
      ...values,
      customer_id: props.customerId?.toString() || '',
    })
    if (code === 0) {
      refresh()
      isAddNoteModalVisible.value = false
    }
  }
  catch (error) {
    console.error(error)
  }
  finally {
    isSubmitting.value = false
  }
}

defineExpose({
  openAddNoteModal,
})
</script>

<template>
  <div class="mt-8">
    <!-- Notes 列表 -->
    <div v-if="isListLoading" class="flex justify-center py-8">
      <ProgressSpinner style="width: 50px; height: 50px" stroke-width="4" />
    </div>

    <div v-else-if="notesList && notesList.length > 0" class="notes-timeline">
      <div
        v-for="(note, index) in notesList" :key="note?.id" class="note-item flex gap-4"
        :class="{ first: index === 0, last: index === notesList.length - 1 }"
      >
        <!-- Timeline dot -->
        <div v-if="notesList.length > 1" class="timeline-dot" />
        <div v-if="notesList.length > 1" class="timeline-line" />

        <!-- Note content -->
        <div class="note-content flex-1 pb-6">
          <div class="note-header mb-2">
            <h4 class="font-semibold text-gray-900">
              {{ note.title }}
            </h4>
            <p class="text-sm text-gray-500">
              {{ dayjs(note.created_at).format('DD MMM YYYY, HH:mm') }}
            </p>
          </div>
          <div class="note-body">
            <p class="text-gray-700 leading-relaxed">
              {{ note.content }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="text-center py-8 text-gray-500">
      <p>No notes available</p>
    </div>

    <MDialog v-model="isAddNoteModalVisible" style="width: 500px">
      <template #header>
        Add Notes and Remarks
      </template>
      <template #default>
        <VForm :validation-schema="addNoteSchema" @submit="handleAddNote">
          <div class="flex">
            <div class="w-40">
              Date and Time
            </div>
            <div class="flex-1">
              {{ dayjs(new Date()).format('DD/MM/YYYY HH:mm') }}
            </div>
          </div>
          <Field v-slot="{ field, errorMessage }" as="div" class="mt-4 flex items-center" name="title">
            <div class="w-40">
              Title
            </div>
            <div class="flex-1 flex flex-col">
              <InputText class="flex-1" v-bind="field" :class="{ 'p-invalid': errorMessage }" />
              <Message v-if="errorMessage" severity="error" variant="simple" class="mt-2">
                {{ errorMessage }}
              </Message>
            </div>
          </Field>
          <Field v-slot="{ field }" as="div" class="mt-4" name="content" label="Content">
            <div class="mb-4">
              Details
            </div>
            <Textarea class="flex-1 w-full" :rows="4" v-bind="field" />
          </Field>

          <div class="flex justify-end gap-4 mt-4">
            <Button class="w-40" label="CANCEL" @click="isAddNoteModalVisible = false" />
            <Button class="w-40" label="SAVE" type="submit" severity="warn" :loading="isSubmitting" />
          </div>
        </VForm>
      </template>
    </MDialog>
  </div>
</template>

<style lang="scss" scoped>
.notes-timeline {
  position: relative;

  .note-item {
    position: relative;
    --left: 2rem;

    .timeline-line {
      position: absolute;
      top: 0;
      left: calc(var(--left) - 1px);
      width: 2px;
      height: 100%;
      background-color: #ffc107;
    }

    .timeline-dot {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: calc(var(--left) - 8px);
      width: 16px;
      height: 16px;
      background-color: #ffc107;
      border-radius: 50%;
    }

    &.first {
      .timeline-line {
        top: 40%;
      }

      // 绘制向上的三角形
      .timeline-dot {
        top: 40%;
        left: calc(var(--left) - 12px);
        border-radius: 0;
        width: 0;
        height: 0;
        background-color: transparent;
        border: 12px solid transparent;
        border-bottom-color: #ffc107;
      }
    }

    &.last {
      .timeline-line {
        height: 50%;
      }
    }

    .note-content {
      background-color: #f5f5f5;
      padding: 16px;
      padding-left: calc(var(--left) + 16px);
      border-radius: 8px;
      margin-bottom: 12px;
    }

  }
}
</style>
