<script setup lang="ts">
import { Constants, Format } from '@shared'
import { toTypedSchema } from '@vee-validate/yup'
import dayjs from 'dayjs'
import { Decimal } from 'decimal.js'
import { Field, Form as VeeForm } from 'vee-validate'
import { ref, watch } from 'vue'
import * as yup from 'yup'
import CustomDialog from '@/components/customDialog/index.vue'
import { usePaymentPlan } from '@/composables/usePaymentPlan'
import { property as propertyApi } from '@/services/flexirates'

interface Props {
  visible: boolean
  propertyList: { label: string | number, value: string | undefined }[]
}

const props = defineProps<Props>()
const emit = defineEmits(['update:visible', 'confirm'])

const editType = ref(Constants.Flexirates.EditPlanType.SKIP_NEXT_PAYMENT)
const selected = ref()
const isDisabledSelected = ref(false)
const confirmVisible = ref(false)

const isSubmitLoading = ref(false)
const showTerms = ref(false)

const scheduleFormRef = ref()
const editFormRef = ref()
const pauseForm = ref({
  startDate: null,
  endDate: null,
  plan: null,
  firstDate: null,
  amount: null,
})

const agreeTerms = ref(false)

const {
  paymentPlanOptions,
  dictPaymentPlan,
  lastPaymentAmount,
  calculatedLastPaymentDate,
  calculateRegularPaymentAmount,
  getPaymentAmount,
  getPaymentPlanSchedule,
} = usePaymentPlan()

const updateForm = ref<{
  paymentPlan: number
  paymentPlanSchedule: number | null
  firstDate: Date | null
  amount: number | null
}>({
  paymentPlan: Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS,
  paymentPlanSchedule: null,
  firstDate: null,
  amount: null,
})

const regularPaymentAmount = ref<string>('')
const numberOfPayments = ref<number>(0)

const editScheduleDateRange = ref<{ start: string | null, end: string | null }>({
  start: null,
  end: null,
})

const isLoading = ref(false)

const nextConfirm = () => {
  confirmVisible.value = true
}

const currentPropertyDetail = ref<Api.FlexiratesPropertyDetailRes | null>(null)

const handlePropertyChange = async (id: string) => {
  try {
    currentPropertyDetail.value = null
    isLoading.value = true
    const { data, code } = await propertyApi.getPropertyDetail(Number(id))
    if (code === 0) {
      currentPropertyDetail.value = data
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    isLoading.value = false
  }
}

const handleConfirm = async () => {
  if (!agreeTerms.value) {
    return window.$toast.add({
      severity: 'warn',
      summary: 'warn',
      detail: 'You must agree to the terms and conditions',
    })
  }
  try {
    isSubmitLoading.value = true

    const sendData = {
      id: currentPropertyDetail.value?.id,
      edit_type: editType.value,
    } as Api.FlexiratesUpdatePropertyScheduleReq

    // 根据不同的编辑类型组装参数
    if (editType.value === Constants.Flexirates.EditPlanType.PAUSE_SCHEDULE) {
      sendData.pause_start_date = dayjs(pauseForm.value.startDate).format('YYYY-MM-DD')
      sendData.pause_end_date = dayjs(pauseForm.value.endDate).format('YYYY-MM-DD')

      // Pause Schedule 也需要这些字段（根据API文档）
      sendData.first_payment_date = dayjs(pauseForm.value.startDate).format('YYYY-MM-DD')
      sendData.payment_plan = currentPropertyDetail.value?.schedule_details?.payment_plan || Constants.Flexirates.Plan.QUARTERLY
      sendData.amount = Number(currentPropertyDetail.value?.next_payment_amount || 0)
    }
    else if (editType.value === Constants.Flexirates.EditPlanType.EDIT_SCHEDULE_DETAILS) {
      // Edit Schedule Details 需要的参数
      const formValues = editFormRef.value?.values
      sendData.first_payment_date = dayjs(formValues.firstDate).format('YYYY-MM-DD')

      // 使用 composable 的辅助函数来获取 payment_plan 和 amount
      const tempModel = {
        payment_plan: formValues.paymentPlan,
        payment_plan_schedule: formValues.paymentPlanSchedule,
        first_payment_date: formValues.firstDate,
        amount: formValues.amount?.toString() || '',
        regular_payment_amount: regularPaymentAmount.value,
        no_of_regular_payment: numberOfPayments.value,
      }

      sendData.payment_plan = getPaymentPlanSchedule(formValues.paymentPlan, tempModel)
      sendData.amount = formValues.paymentPlan === Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT ? Number(getPaymentAmount(formValues.paymentPlan, tempModel, Number(currentPropertyDetail.value?.total_remaining) || 0) || 0) : 0
    }

    const { code } = await propertyApi.updatePropertySchedule(sendData)

    if (code === 0) {
      window.$toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Update schedule success',
      })
      emit('confirm')
      confirmVisible.value = false
      emit('update:visible', false)
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    isSubmitLoading.value = false
  }
}

const handleUpdateVisible = (val: boolean) => {
  emit('update:visible', val)
}

// 处理暂停开始日期变化的验证逻辑
const handlePauseStartDateChange = (newStartDate: Date | null) => {
  // 如果没有选择开始日期，直接返回
  if (!newStartDate) {
    return
  }

  // 如果已经选择了结束日期，检查是否需要清空
  if (pauseForm.value.endDate) {
    const startDate = dayjs(newStartDate)
    const endDate = dayjs(pauseForm.value.endDate)
    // 如果结束日期小于或等于开始日期，清空结束日期
    if (endDate.isBefore(startDate) || endDate.isSame(startDate, 'day')) {
      pauseForm.value.endDate = null
      // 如果有表单引用，也清空表单中的值
      if (scheduleFormRef.value) {
        scheduleFormRef.value.setFieldValue('endDate', null)
      }
    }
  }
}

// 添加验证 schema
const editFormSchema = toTypedSchema(yup.object({
  paymentPlan: yup.number().min(1, 'Payment plan is required'),
  paymentPlanSchedule: yup.number().when('paymentPlan', {
    is: (val: number) => [Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS, Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT].includes(val),
    then: schema => schema.min(1, 'Payment schedule is required'),
    otherwise: schema => schema.optional(),
  }),
  firstDate: yup.date()
    .min(dayjs().startOf('day').toDate(), 'First payment date must be today or in the future'),
  amount: yup.number().when('paymentPlan', {
    is: Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT,
    then: schema => schema.min(0.01, 'Amount is required'),
    otherwise: schema => schema.optional(),
  }),
}))

const handlePaymentPlanScheduleChange = (val: number) => {
  const dateRange = currentPropertyDetail.value?.date_range?.first_payment_dates?.[val]
  if (dateRange) {
    editScheduleDateRange.value.start = dateRange.start
    editScheduleDateRange.value.end = dateRange.end
    if (editFormRef.value) {
      updateForm.value.firstDate = dayjs(dateRange.start).toDate()
      editFormRef.value.setFieldValue('firstDate', dayjs(dateRange.start).toDate())
    }
  }
}

const setPropertyDetailAndLoad = (id: string) => {
  isDisabledSelected.value = true
  selected.value = id
  handlePropertyChange(id)
}

// 添加 watch 监听器
watch([() => updateForm.value.amount, () => updateForm.value.firstDate, () => updateForm.value.paymentPlanSchedule, () => updateForm.value.paymentPlan, () => currentPropertyDetail.value?.final_payment_date], () => {
  // Create a temporary model with the form values
  const tempModel = {
    payment_plan: updateForm.value.paymentPlan,
    payment_plan_schedule: updateForm.value.paymentPlanSchedule || 0,
    first_payment_date: updateForm.value.firstDate || new Date(),
    amount: updateForm.value.amount?.toString() || '',
    regular_payment_amount: regularPaymentAmount.value,
    no_of_regular_payment: numberOfPayments.value,
  }

  if (currentPropertyDetail.value?.total_remaining && currentPropertyDetail.value.final_payment_date) {
    calculateRegularPaymentAmount(
      tempModel,
      updateForm.value.paymentPlan,
      Number(currentPropertyDetail.value.total_remaining),
      currentPropertyDetail.value.final_payment_date,
    )

    // Update local values
    regularPaymentAmount.value = tempModel.regular_payment_amount || ''
    numberOfPayments.value = tempModel.no_of_regular_payment || 0
  }
}, { immediate: true })

defineExpose({
  setPropertyDetailAndLoad,
})
</script>

<template>
  <div class="dialog">
    <CustomDialog :visible="props.visible" title="Edit Schedule" @update:visible="handleUpdateVisible">
      <template #content>
        <div class="w-full md:w-[650px]">
          <div class="text-[16px] font-semibold mb-6">
            <div class="py-4">
              Select Property:
            </div>
            <div>
              <Select
                v-model="selected" :options="props.propertyList" option-label="label" option-value="value"
                placeholder="— Please Select —" class="w-full" :loading="isLoading" :disabled="isDisabledSelected"
                @value-change="handlePropertyChange"
              />
            </div>
          </div>
          <template v-if="currentPropertyDetail">
            <div class="mb-8">
              <div class="flex flex-col md:flex-row md:justify-between gap-2 md:gap-0">
                <div class="flex items-center gap-4">
                  <RadioButton
                    v-model="editType" input-id="skip" name="skip"
                    :value="Constants.Flexirates.EditPlanType.SKIP_NEXT_PAYMENT"
                  />
                  <label for="skip" class="edit-label">Skip Next Payment</label>
                </div>
                <!-- <div class="flex items-center gap-4">
                  <RadioButton
                    v-model="editType" input-id="pause" name="pause"
                    :value="Constants.Flexirates.EditPlanType.PAUSE_SCHEDULE"
                  />
                  <label for="pause" class="edit-label">Pause Schedule</label>
                </div> -->
                <div class="flex items-center gap-4">
                  <RadioButton
                    v-model="editType" input-id="edit" name="edit"
                    :value="Constants.Flexirates.EditPlanType.EDIT_SCHEDULE_DETAILS"
                  />
                  <label for="edit" class="edit-label">Edit Schedule details</label>
                </div>
              </div>
            </div>

            <template v-if="editType === Constants.Flexirates.EditPlanType.SKIP_NEXT_PAYMENT">
              <div>
                <div class="flex justify-center text-[18px] pb-8 font-bold">
                  You have chosen to skip your next scheduled payment.
                </div>
                <div class="flex flex-col md:flex-row md:justify-between gap-2 md:gap-0">
                  <div class="flex flex-col gap-2">
                    <div>
                      <span class="font-bold">Skipped Scheduled Payment Date :
                      </span>
                      <span> {{ dayjs(currentPropertyDetail?.next_payment_date).format('DD/MM/YYYY') }} </span>
                    </div>
                    <div>
                      <span class="font-bold">
                        Regular Payment Amount :
                      </span>
                      <span>
                        {{ Format.formatAmount(currentPropertyDetail?.next_payment_amount) }}
                      </span>
                    </div>
                  </div>
                  <div>
                    <div class="flex flex-col gap-2">
                      <div>
                        <span v-if="currentPropertyDetail" class="font-bold">
                          Next Scheduled Payment Date :
                        </span>
                        <span> {{ dayjs(currentPropertyDetail?.adjust_next_payment_date).format('DD/MM/YYYY') }} </span>
                      </div>
                      <div>
                        <span class="font-bold">
                          Adjusted Regular Payment Amount :
                        </span>
                        <span>
                          {{ Format.formatAmount(currentPropertyDetail?.adjust_next_payment_amount) }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="skip-tips">
                  Your regular payment amount has been adjusted to align with your selected plan.To update
                  your
                  payment
                  frequency or regular payment amount, click "Edit Schedule Details" in the options above.
                </div>
              </div>
            </template>
            <template v-else-if="editType === Constants.Flexirates.EditPlanType.PAUSE_SCHEDULE">
              <ScrollPanel style="width: 100%; height: 180px">
                <VeeForm ref="scheduleFormRef" v-slot="{ values }">
                  <div class="text-[18px] font-semibold pb-2">
                    Select the pause period
                  </div>
                  <!-- <div class="text-sm">
                    Maximum Pause Period is 3 months.
                  </div> -->
                  <div class="datePick">
                    <Field
                      v-slot="{ field, errorMessage, handleChange }" v-model="pauseForm.startDate" as="div"
                      name="startDate" class="schedule-form-item"
                    >
                      <div class="flex items-center">
                        <label class="schedule-form-label">Pause Start Date<span>*</span>:</label>
                        <DatePicker
                          v-model="field.value" show-icon fluid icon-display="input" input-id="startDate"
                          class="w-full" :min-date="dayjs(currentPropertyDetail?.date_range.pause?.start).toDate()"
                          :max-date="dayjs(currentPropertyDetail?.date_range.pause?.end).add(1, 'day').toDate()"
                          date-format="dd/mm/yy" @value-change="(newValue) => {
                            handleChange(newValue)
                            handlePauseStartDateChange(newValue as Date | null)
                          }"
                        />
                      </div>
                      <Message v-if="errorMessage" class="ml-46 mt-2" severity="error" size="small" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </Field>
                    <Field
                      v-slot="{ field, errorMessage, handleChange }" v-model="pauseForm.endDate" as="div"
                      name="endDate" class="schedule-form-item"
                    >
                      <div class="flex items-center">
                        <label class="schedule-form-label">Pause End Date<span>*</span>:</label>
                        <DatePicker
                          v-model="field.value" :disabled="pauseForm.startDate === null"
                          :min-date="dayjs(values.startDate).toDate()"
                          :max-date="dayjs(currentPropertyDetail?.date_range.pause?.end).toDate()"
                          date-format="dd/mm/yy" show-icon fluid icon-display="input" input-id="startDate"
                          class="w-full" @value-change="handleChange"
                        />
                      </div>
                      <Message v-if="errorMessage" class="ml-46 mt-2" severity="error" size="small" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </Field>
                  </div>
                </VeeForm>
              </ScrollPanel>
            </template>
            <template v-if="editType === Constants.Flexirates.EditPlanType.EDIT_SCHEDULE_DETAILS">
              <div class="text-[18px] font-semibold pb-2">
                Current Schedule Details
              </div>
              <div class="curren-details grid grid-cols-1 md:grid-cols-2 mt-4 gap-y-6">
                <div class="flex  gap-4 font-semibold">
                  <div>
                    Payment Plan:
                  </div>
                  <div>
                    {{ currentPropertyDetail?.schedule_details.payment_plan_desc }}
                  </div>
                </div>
                <div class="flex  gap-4 font-semibold ">
                  <div>
                    first Payment Date:
                  </div>
                  <div class="">
                    {{ dayjs(currentPropertyDetail?.first_payment_date).format('DD/MM/YYYY') }}
                  </div>
                </div>
                <div class="flex  gap-4 font-semibold ">
                  <div>
                    Regular Payment Amount:
                  </div>
                  <div>
                    {{ Format.formatAmount(currentPropertyDetail?.next_payment_amount) }}
                  </div>
                </div>

                <div class="flex gap-4 font-semibold ">
                  <div>
                    Last Payment Date:
                  </div>
                  <div class="flex-1">
                    {{ dayjs(currentPropertyDetail?.final_payment_date).format('DD/MM/YYYY') }}
                  </div>
                </div>
                <!-- 当前房产剩余未付总额 -->
                <div class="flex  gap-4 font-semibold ">
                  <div>
                    Remaining Amount:
                  </div>
                  <div>
                    {{ Format.formatAmount(currentPropertyDetail?.total_remaining) }}
                  </div>
                </div>
              </div>
              <div class="text-[18px] font-semibold pb-2">
                Update Schedule
              </div>
              <VeeForm ref="editFormRef" v-slot="{ values }" :validation-schema="editFormSchema">
                <div class="update-form">
                  <Field
                    v-slot="{ field, errorMessage, handleChange }" v-model="updateForm.paymentPlan" as="div"
                    name="paymentPlan" class="schedule-form-item"
                  >
                    <div class="flex items-center">
                      <label class="schedule-form-label">Payment Plan<span>*</span>:</label>
                      <Select
                        v-model="field.value" :options="paymentPlanOptions" option-label="label"
                        option-value="value" placeholder="Select a Payment Plan" class="w-full"
                        @value-change="handleChange"
                      />
                    </div>
                    <Message v-if="errorMessage" class="ml-46 mt-2" severity="error" size="small" variant="simple">
                      {{ errorMessage }}
                    </Message>
                  </Field>
                  <Field
                    v-if="[Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS, Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT].includes(values?.paymentPlan as number)"
                    v-slot="{ field, errorMessage, handleChange }" v-model="updateForm.paymentPlanSchedule" as="div"
                    name="paymentPlanSchedule" class="schedule-form-item"
                  >
                    <div class="flex items-center">
                      <label class="schedule-form-label">Payment Schedule<span>*</span>:</label>
                      <Select
                        v-model="field.value" :options="dictPaymentPlan" option-label="label" option-value="value"
                        placeholder="Select a Payment Schedule" class="w-full" @value-change="(e: number) => {
                          handleChange(e)
                          handlePaymentPlanScheduleChange(e)
                        }"
                      />
                    </div>
                    <Message v-if="errorMessage" class="ml-46 mt-2" severity="error" size="small" variant="simple">
                      {{ errorMessage }}
                    </Message>
                  </Field>
                  <Field
                    v-slot="{ field, errorMessage, handleChange }" v-model="updateForm.firstDate" as="div"
                    name="firstDate" class="schedule-form-item"
                  >
                    <div class="flex items-center">
                      <label class="schedule-form-label">First Payment Date<span>*</span>:</label>
                      <DatePicker
                        v-model="field.value" :disabled="values.paymentPlanSchedule === null" :min-date="values.paymentPlan === Constants.Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW
                          ? dayjs().add(3, 'day').toDate()
                          : (editScheduleDateRange.start ? dayjs(editScheduleDateRange.start).toDate() : new Date())
                        "
                        :max-date="values.paymentPlan === Constants.Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW
                          ? dayjs(currentPropertyDetail?.final_payment_date).subtract(1, 'day').toDate()
                          : (editScheduleDateRange.end ? dayjs(editScheduleDateRange.end).toDate() : dayjs(currentPropertyDetail?.final_payment_date).toDate())"
                        date-format="dd/mm/yy" show-icon fluid icon-display="input" input-id="firstDate" class="w-full"
                        :class="{ 'p-invalid': errorMessage }" @value-change="handleChange"
                      />
                    </div>
                    <Message v-if="errorMessage" class="ml-46 mt-2" severity="error" size="small" variant="simple">
                      {{ errorMessage }}
                    </Message>
                  </Field>
                  <Field
                    v-if="values?.paymentPlan === Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT"
                    v-slot="{ field, errorMessage, handleChange }" v-model="updateForm.amount" as="div" name="amount"
                    class="schedule-form-item"
                  >
                    <div class="flex items-center">
                      <label class="schedule-form-label">Amount<span>*</span>:</label>
                      <InputNumber
                        v-model="field.value" :min-fraction-digits="2" :max-fraction-digits="2"
                        :max="Number(currentPropertyDetail?.total_remaining || 0)" input-id="amount" fluid
                        @value-change="handleChange"
                      />
                    </div>
                    <Message v-if="errorMessage" class="ml-46 mt-2" severity="error" size="small" variant="simple">
                      {{ errorMessage }}
                    </Message>
                  </Field>
                </div>

                <!-- 显示信息部分 -->
                <div class="form-item flex">
                  <label class="schedule-form-label">Last payment date :</label>
                  <div class="form-item__content">
                    <span class="ml-2">

                      <template v-if="values?.paymentPlan === Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT && calculatedLastPaymentDate">
                        {{ dayjs(calculatedLastPaymentDate).format('DD/MM/YYYY') }}
                      </template>
                      <template v-else>
                        {{ currentPropertyDetail?.final_payment_date ? dayjs(currentPropertyDetail?.final_payment_date).format('DD/MM/YYYY') : '' }}
                      </template>
                    </span>
                  </div>
                </div>
                <div
                  v-if="values?.paymentPlan === Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS"
                  class="form-item flex mt-4"
                >
                  <label class="schedule-form-label">Regular payment amount :</label>
                  <div class="form-item__content">
                    <span class="ml-2">
                      {{ regularPaymentAmount ? Format.formatAmount(new Decimal(regularPaymentAmount).toFixed(2)) : ''
                      }}
                    </span>
                  </div>
                </div>
                <div class="form-item flex mt-4">
                  <label class="schedule-form-label">Last payment Amount :</label>
                  <div class="form-item__content">
                    <span class="ml-2">
                      <template v-if="values?.paymentPlan === Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT && Number(values.amount) > 0">
                        <template v-if="Number(lastPaymentAmount) > 0">
                          {{ Format.formatAmount(lastPaymentAmount) }}
                        </template>
                        <template v-else-if="Number(lastPaymentAmount) === 0">
                          {{ Format.formatAmount(new Decimal(values.amount).toFixed(2)) }}
                        </template>
                      </template>
                      <template v-else-if="values?.paymentPlan === Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS && lastPaymentAmount">
                        {{ Format.formatAmount(lastPaymentAmount) }}
                      </template>
                      <template v-else-if="values?.paymentPlan === Constants.Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW">
                        {{ Format.formatAmount(new Decimal(currentPropertyDetail?.total_remaining || 0).toFixed(2)) }}
                      </template>
                    </span>
                  </div>
                </div>
                <div class="form-item flex mt-4">
                  <label class="schedule-form-label">Number of payments during
                    {{ dayjs().year() }}
                    <template v-if="currentPropertyDetail?.final_payment_date">
                      - {{ dayjs(currentPropertyDetail.final_payment_date).year() }}
                    </template>
                    :</label>
                  <span class="ml-2">
                    <template v-if="values?.paymentPlan === Constants.Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW">
                      1
                    </template>
                    <template v-else>
                      {{ numberOfPayments ? numberOfPayments : '' }}
                    </template>
                  </span>
                </div>
              </VeeForm>
            </template>
            <div class="flex justify-end mt-4">
              <Button label="NEXT" severity="warn" class="btn" @click="nextConfirm" />
            </div>
          </template>
        </div>
      </template>
    </CustomDialog>

    <!-- 确认对话框 -->
    <CustomDialog :visible="confirmVisible" title="Confirmation" @update:visible="(val) => (confirmVisible = val)">
      <template #content>
        <div class="text-[20px] font-semibold pb-2 flex mt-4 mb-4">
          Important Information before confirming your changes.
        </div>
        <ul class="paragraph">
          <li class="mb-4">
            If your FlexiRates account has 3 or more declined payments within a financial year, your
            registration
            may be
            canceled.
          </li>
          <li class="mb-4">
            If a payment is skipped or fails, your regular payment amount may adjust from the original
            setup.
          </li>
          <li class="mb-4">
            You may edit a schedule up to 5 times per property, including:
            <ul class="paragraph">
              <li class="ml-4">
                Skipping a payment schedule
              </li>
              <li class="ml-4">
                Pausing a schedule
              </li>
              <li class="ml-4">
                Editing schedule details
              </li>
            </ul>
          </li>
        </ul>
        <div>
          If you require additional edits, please <span class="font-bold text-[#1b1548] cursor-pointer">contact our
            support
            team</span> for assistance.
        </div>
        <div class="flex items-center mt-4 gap-4">
          <Checkbox v-model="agreeTerms" binary input-id="terms" />
          <label for="terms" class="text-sm">
            By ticking, you are confirming that you have read, understood andd agree to the
            <span class="font-bold cursor-pointer" @click.stop="showTerms = true">
              Terms and Conditions
            </span>.
          </label>
        </div>
        <div class="font-bold text-2xl tips-wrap flex justify-center items-center ">
          <span class="text-[#545454]">Edit Schedule Count : </span>
          <span class="ml-2">{{ currentPropertyDetail?.schedule_details.edited_count }} of
            {{ currentPropertyDetail?.schedule_details.editable_count }}</span>
        </div>
        <div class=" flex justify-end mt-4">
          <Button label="CONFIRM" severity="warn" class="btn" :loading="isSubmitLoading" @click="handleConfirm" />
        </div>
      </template>
    </CustomDialog>
    <TermsAndConditions v-model="showTerms" />
  </div>
</template>

<style scoped lang="scss">
.paragraph {
  list-style: disc;
  margin-left: 1.5rem;
  line-height: 2;
}

.curren-details {
  border-bottom: 1px solid #545454;
  padding-bottom: 1.2rem;
  margin-bottom: 1rem;
}

.datePick {
  padding-bottom: .5rem;
  margin-bottom: 1rem;
}

.schedule-form-item {
  margin: 15px 0;
}

.schedule-form-label {
  font-weight: 600;
  width: 300px;

  span {
    color: #ef4f27;
  }
}

.edit-label {
  font-size: 16px;
  font-weight: 600;
}

.skip-tips,
.tips-wrap {
  border: 2px solid #0073cf;
  padding: 1rem;
  margin-top: 2.5rem;
  color: #EB001B;
}

.btn {
  display: inline-block;
  padding: 10px 30px;
  font-size: 20px;
}
</style>
