import { Constants } from '@shared'
import dayjs from 'dayjs'
import { Decimal } from 'decimal.js'
import { computed, ref } from 'vue'
import { calculateDynamicLastPaymentDate, calculatePaymentCountByLoop } from '@/utils/date'

export interface PaymentPlanModel {
  payment_plan: number
  payment_plan_schedule: number
  first_payment_date: Date | string
  amount: string
  regular_payment_amount?: string
  no_of_regular_payment?: number
}

export interface PaymentDict {
  label: string
  value: number | string
}

export function usePaymentPlan() {
  const isShowFullAmountAndQuarterly = ref(dayjs().isBefore(dayjs('2025-09-27')))

  // Payment Plan options
  const paymentPlanOptions = computed<PaymentDict[]>(() => [
    {
      label: 'Calculated Installments',
      value: Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS,
    },
    {
      label: 'Custom Installment Amount',
      value: Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT,
    },
    isShowFullAmountAndQuarterly.value && {
      label: 'Pay Full Amount',
      value: Constants.Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW,
    },
  ].filter(Boolean) as PaymentDict[])

  const dictPaymentPlan = computed<PaymentDict[]>(() => [
    {
      label: 'Weekly',
      value: Constants.Flexirates.Plan.WEEKLY,
    },
    {
      label: 'Fortnightly',
      value: Constants.Flexirates.Plan.FORTNIGHTLY,
    },
    {
      label: 'Monthly',
      value: Constants.Flexirates.Plan.MONTHLY,
    },
    isShowFullAmountAndQuarterly.value && {
      label: 'Quarterly',
      value: Constants.Flexirates.Plan.QUARTERLY,
    },
  ].filter(Boolean) as PaymentDict[])

  const lastPaymentAmount = ref<string>('')
  const calculatedLastPaymentDate = ref<string>('')

  const maxDate = computed(() => {
    const now = dayjs()
    const deadline = dayjs('2025-09-27')

    if (now.isAfter(deadline)) {
      return now.add(30, 'day').toDate()
    }
    else {
      return dayjs('2025-09-30').toDate()
    }
  })

  const calculateCustomInstallmentPayments = (
    customAmount: string,
    model: PaymentPlanModel,
    totalAmountDue: number,
    lastPaymentDate: string,
  ) => {
    if (!model.first_payment_date || !model.payment_plan_schedule || !customAmount || Decimal(customAmount).lte(0)) {
      if (model.no_of_regular_payment !== undefined) {
        model.no_of_regular_payment = 0
      }
      lastPaymentAmount.value = '0'
      calculatedLastPaymentDate.value = ''
      return
    }

    const totalDue = Decimal(totalAmountDue || 0)
    const amount = Decimal(customAmount)

    const firstPaymentDateDate = dayjs(model.first_payment_date)
    const endOfPeriod = dayjs(lastPaymentDate)

    // 当前周期数
    let possiblePayments = calculatePaymentCountByLoop(
      firstPaymentDateDate,
      endOfPeriod,
      model.payment_plan_schedule,
    )

    // 当前剩余金额
    let finalPaymentAmount = '0'

    // 如果是自定义输入金额, 则输入金额然后算出期数
    if (model.payment_plan === Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT) {
      const tempPayments = totalDue.div(amount).ceil().toNumber()
      possiblePayments = Math.min(tempPayments, possiblePayments)

      // Calculate the final payment amount
      if (possiblePayments > 0) {
        const regularPaymentsTotal = amount.mul(possiblePayments - 1)
        finalPaymentAmount = totalDue.sub(regularPaymentsTotal).toFixed(2, Decimal.ROUND_HALF_UP)
      }
    }
    else {
      finalPaymentAmount = totalDue.sub(amount.mul(possiblePayments)).toFixed(2, Decimal.ROUND_HALF_UP)
    }

    lastPaymentAmount.value = finalPaymentAmount

    if (model.no_of_regular_payment !== undefined) {
      model.no_of_regular_payment = possiblePayments
    }

    calculatedLastPaymentDate.value = calculateDynamicLastPaymentDate(
      model.first_payment_date,
      model.payment_plan_schedule,
      model.no_of_regular_payment || possiblePayments,
    )
  }

  const calculateCalculatedInstallments = (
    model: PaymentPlanModel,
    totalAmountDue: number,
    lastPaymentDate: string,
  ) => {
    if (!model.first_payment_date || !model.payment_plan_schedule || !lastPaymentDate) {
      if (model.no_of_regular_payment !== undefined) {
        model.no_of_regular_payment = 0
      }
      if (model.regular_payment_amount !== undefined) {
        model.regular_payment_amount = '0'
      }
      lastPaymentAmount.value = '0'
      return
    }

    const totalDue = totalAmountDue || 1
    const firstPaymentDateDate = dayjs(model.first_payment_date)
    const lastPaymentDateDate = dayjs(lastPaymentDate)

    let paymentCount = 0
    let amount = '0'

    if (model.payment_plan_schedule === Constants.Flexirates.Plan.FULL_AMOUNT) {
      paymentCount = 1
      amount = Decimal(totalDue).toFixed(2, Decimal.ROUND_HALF_UP)
      lastPaymentAmount.value = amount
    }
    else {
      paymentCount = calculatePaymentCountByLoop(
        firstPaymentDateDate,
        lastPaymentDateDate,
        model.payment_plan_schedule,
      )

      // Calculate regular payment amount (rounded down)
      const regularAmount = Decimal(totalDue).div(paymentCount)
      const regularAmountRounded = regularAmount.toFixed(0, Decimal.ROUND_DOWN)
      amount = regularAmountRounded

      // Calculate last payment amount
      // Last payment = regular payment + remainder
      const remainderPerPayment = regularAmount.sub(regularAmountRounded)
      const totalRemainder = remainderPerPayment.mul(paymentCount)
      const lastPayment = Decimal(regularAmountRounded).add(totalRemainder)

      lastPaymentAmount.value = lastPayment.toFixed(2, Decimal.ROUND_HALF_UP)
    }

    if (model.regular_payment_amount !== undefined) {
      model.regular_payment_amount = amount
    }
    if (model.no_of_regular_payment !== undefined) {
      model.no_of_regular_payment = Math.floor(paymentCount)
    }
  }

  const calculatePayFullAmount = (
    model: PaymentPlanModel,
    totalAmountDue: number,
  ) => {
    const amount = Decimal(totalAmountDue || 0).toFixed(2, Decimal.ROUND_HALF_UP)

    if (model.regular_payment_amount !== undefined) {
      model.regular_payment_amount = amount
    }
    if (model.no_of_regular_payment !== undefined) {
      model.no_of_regular_payment = 1
    }

    lastPaymentAmount.value = amount
  }

  const calculateRegularPaymentAmount = (
    model: PaymentPlanModel,
    paymentPlan: number,
    totalAmountDue: number,
    lastPaymentDate: string,
  ) => {
    // Delegate to specific calculation functions based on payment plan
    switch (paymentPlan) {
      case Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS:
        calculateCalculatedInstallments(model, totalAmountDue, lastPaymentDate)
        break
      case Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT:
        if (model.amount) {
          calculateCustomInstallmentPayments(model.amount, model, totalAmountDue, lastPaymentDate)
        }
        break
      case Constants.Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW:
        calculatePayFullAmount(model, totalAmountDue)
        break
      default:
        // Reset values for unknown payment plan
        if (model.no_of_regular_payment !== undefined) {
          model.no_of_regular_payment = 0
        }
        if (model.regular_payment_amount !== undefined) {
          model.regular_payment_amount = '0'
        }
        lastPaymentAmount.value = '0'
    }
  }

  const calculateEndYear = (firstPaymentDate: Date, paymentSchedule: number, numberOfPayments: number): number => {
    if (!firstPaymentDate || !paymentSchedule || numberOfPayments <= 0) {
      return dayjs().year()
    }

    const lastPaymentDateStr = calculateDynamicLastPaymentDate(firstPaymentDate, paymentSchedule, numberOfPayments)
    return dayjs(lastPaymentDateStr).year()
  }

  const getPaymentAmount = (
    paymentPlan: number,
    model: PaymentPlanModel,
    totalAmountDue: number,
  ) => {
    if (paymentPlan === Constants.Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW) {
      return totalAmountDue || 0
    }
    else if (paymentPlan === Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS) {
      return model.regular_payment_amount
    }
    else {
      return model.amount
    }
  }

  const getPaymentPlanSchedule = (
    paymentPlan: number,
    model: PaymentPlanModel,
  ) => {
    if (paymentPlan === Constants.Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW) {
      return Constants.Flexirates.Plan.FULL_AMOUNT
    }
    return model.payment_plan_schedule
  }

  return {
    paymentPlanOptions,
    dictPaymentPlan,
    lastPaymentAmount,
    calculatedLastPaymentDate,
    maxDate,
    calculateCalculatedInstallments,
    calculateCustomInstallmentPayments,
    calculatePayFullAmount,
    calculateRegularPaymentAmount,
    calculateEndYear,
    getPaymentAmount,
    getPaymentPlanSchedule,
  }
}
