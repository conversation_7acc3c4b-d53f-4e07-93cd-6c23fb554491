declare namespace Api {
  /**
   * Request payload for the login endpoint.
   *
   * @property email - The user's email address.
   * @property password - The user's password.
   * @property rememberMe - Whether to remember the user's login.
   */
  interface FlexiratesUserLoginReq {
    /**
     * 邮箱，email登录：与 mobile_phone 二选一必填
     */
    email: string

    country_code?: string
    /**
     * 移动号码，mobile登录：与 email 二选一必填
     */
    mobile_phone?: string
    /**
     * 密码，email登录：email不为空时必填
     */
    password: string
    /**
     * 验证码，mobile登录：mobile_phone不为空时必填
     */
    verification_code: string
    rememberMe: boolean
    google_token: string
  }

  type FlexiratesUserLoginReq = LoginWithEmail | LoginWithMobile

  interface FlexiratesUserLoginRes {
    refresh_token: string
    access_token: string
    expires_in: number
  }

  interface RegisterAccountInfoReq {
    property_number: string
    verification_code: string
  }

  interface RegisterAccountInfoRes {
    assessment_number: string
    end_date: string
    full_amount: string
    instalment_1_amount: string
    instalment_1_due: string
    instalment_2_mount: string
    instalment_2_due: string
    instalment_3_amount: string
    instalment_3_due: string
    instalment_4_amount: string
    instalment_4_due: string
    postcode: string
    property_address: string
    property_suburb: string
    start_date: string
    active_register_flag: string
    active_registration_id: string
    allow_quaterly: string
    allow_register: string
    message: string
    scheduled_payments_flag: string
    payment_plan?: {
      label: string
      value: string
    }[]
  }

  interface FlexiratesUserRegisterReq {
    bank_id: number
    email: string
    first_name: string
    /**
     * 是否接受邮件通知，默认0
     */
    is_notice?: string | number
    last_name: string
    mobile: string
    password: string
    payment_frequency: {
      /**
       * 每个周期支付的金额
       */
      amount?: number | null
      /**
       * 日期格式
       */
      first_payment_date: string
      /**
       * 字典，可选值：{
       * 1: 'Weekly',
       * 2: 'Forthnightly',
       * 3: 'Quarterly', // 默认值
       * 4: 'Monthly',
       * 5: 'Full Amount'
       * }
       */
      payment_plan: number
    }
    /**
     * 页面上所需对应信息从接口【公共部分-获取Property信息】获取
     */
    property: {
      property_number: string
      verification_code: string
    }
  }

  interface FlexiratesUserRegisterCard {
    card_number: string
    security_code: string
    name_on_card: string
    expiration_year: string
    expiration_month: string
  }

  interface FlexiratesUserRegisterBank {
    bsb: string
    account_no: string
    account_name: string
  }

  interface FlexiratesUserRegisterRes {
    message: string
    access_token: string
    refresh_token: string
  }

  interface SendVerificationCodeReq {
    mobile: string
    country_code: string
  }

  interface SendVerificationCodeRes {
    message: string
  }

  interface SendResetPasswordEmailReq {
    email: string
    path: string
  }

  interface SendResetPasswordEmailRes {
    message: string
  }

  interface ResetPasswordReq {
    token: string
    new_password: string
  }

  interface ResetPasswordRes {
    message: string
  }

  interface FlexiratesUserRegisterCreateBankingReq {
    /**
     * 银行账号，type=1 时必填
     */
    bank: FlexiratesUserRegisterCreateBank
    /**
     * 信用卡，type=2 时必填
     */
    card: FlexiratesUserRegisterCreateCard
    /**
     * 重定向地址
     */
    return_url: string
    /**
     * 支付类型，可选值：{
     * 1: 'Bank Account',
     * 2: 'Card Number'
     * }
     */
    type: number
    /**
     * 权重，1:Primary 2:Secondary
     */
    weight: number
  }

  interface FlexiratesUserRegisterCreateBank {
    /**
     * 账号名称
     */
    account_name: string
    /**
     * 银行账号
     */
    account_no: string
    bsb: string
    /**
     * 昵称
     */
    nickname?: string
  }

  interface FlexiratesUserRegisterCreateCard {
    /**
     * 卡号
     */
    card_number: string
    /**
     * 城市
     */
    city: string
    /**
     * 国家代码
     */
    country_iso2: string
    /**
     * 邮箱
     */
    email: string
    /**
     * 月份，MM
     */
    expiration_month: string
    /**
     * 年份，YYYY
     */
    expiration_year: string
    /**
     * 名
     */
    first_name: string
    /**
     * 姓
     */
    last_name: string
    /**
     * 地址1
     */
    line_1: string
    /**
     * 地址2
     */
    line_2?: string
    /**
     * 持卡名称
     */
    name_on_card: string
    /**
     * 昵称
     */
    nickname?: string
    /**
     * 电话
     */
    phone: string
    /**
     * 邮编
     */
    postcode: string
    /**
     * CVV
     */
    security_code: string
    /**
     * 州
     */
    state: string
  }

  interface FlexiratesUserRegisterCreateBankingRes {
    html?: string
    banking_id?: number
  }

  /**
   * User Activity Log List Request Parameters
   */
  interface UserActivityLogListReq extends CommonSearchListParams {
    /**
     * 页码
     */
    page?: number
    /**
     * 每页条数
     */
    page_size?: number

    activity_types?: string
  }

  /**
   * User Activity Log Item
   */
  interface UserActivityLogItem {
    id: number
    customer_id: string
    activity_type: number
    /**
     * 活动详情
     */
    activity_detail: string
    activity_status: number
    /**
     * 创建时间
     */
    created_at: string
    /**
     * 活动类型描述
     */
    activity_type_desc: string
  }

  /**
   * User Activity Log List Response Data
   */
  interface UserActivityLogListData {
    /**
     * 活动日志列表
     */
    data: UserActivityLogItem[]
    /**
     * 当前页码
     */
    current_page: number
    /**
     * 每页条数
     */
    per_page: number
    /**
     * 总条数
     */
    total: number
  }

  /**
   * User Activity Log List Response
   */
  interface UserActivityLogListRes extends CommonRes<UserActivityLogListData> {
    code: number
    message: string
    data: UserActivityLogListData
    total?: number
  }
}
