declare namespace Api {

  interface FlexiratesGetPropertyListRes extends CommonRes {
    data: CommonListRes<FlexiratesGetPropertyListInfo[]>
  }
  interface FlexiratesGetPropertyDetailRes extends CommonRes {
    data: PropertyDetailInfo
  }
  interface PropertyInfo {
    active_register_flag: string
    active_registration_id: string
    allow_quaterly: string
    allow_register: string
    assessment_number: string
    end_date: string
    full_amount: string
    instalment_1_amount: string
    instalment_1_due: string
    instalment_2_due: string
    instalment_2_mount: string
    instalment_3_amount: string
    instalment_3_due: string
    instalment_4_amount: string
    instalment_4_due: string
    message: string
    postcode: string
    property_address: string
    property_suburb: string
    scheduled_payments_flag: string
    start_date: string
  }

  interface FlexiratesGetPropertyListInfo {
    address?: string
    id?: number
    next_payment_date?: string
    nickname?: string
    property_number?: string
    schedule_status?: number
    schedule_status_desc?: string
  }
  interface FlexiratesCreatePropertyReq {
    /**
     * 每个周期支付的金额
     */
    amount: number | null
    /**
     * 日期格式
     */
    first_payment_date: string
    /**
     * 字典，可选值：{
     * 1: 'Weekly',
     * 2: 'Forthnightly',
     * 3: 'Quarterly', // 默认值
     * 4: 'Monthly',
     * 5: 'Full Amount'
     * }
     */
    payment_plan: number | null
    /**
     * 从新增支付方式的接口返回，如果是3D跳转，也会带有这个ID
     */
    primary_payment_method_id: string
    property_number: string
    /**
     * 卡3d验证跳转url
     */
    return_url: string
    /**
     * 同上
     */
    secondary_payment_method_id: string
    verification_code: string
  }
  interface FlexiratesDeletePropertyReq {
    property_ids: [number]
  }
  interface FlexiratesNotifyDeleteReq {
    /**
     * 多个用英文逗号隔开，is_all为空时必填，ids不为空且is_all=1时按ids删除
     */
    ids?: string
    /**
     * 是否删除全部，0、否 1、是，ids为空时必填
     */
    is_all?: number
  }

  interface FlexiratesChangeNickNameReq {
    /**
     * ID 编号
     */
    id: number
    /**
     * 昵称
     */
    nickname: string
  }

  interface FlexiratesEditBankReq {
    account_name: string
    account_no: string
    bsb: string
    /**
     * ID 编号
     */
    id: string
  }

  interface FlexiratesAddCardOrBankReq {
    /**
     * 银行账号，type=1 时必填
     */
    bank?: Bank
    /**
     * 信用卡，type=2 时必填
     */
    card?: Card
    /**
     * 重定向地址
     */
    return_url: string
    /**
     * 支付类型，可选值：{
     * 1: 'Bank Account',
     * 2: 'Card Number'
     * }
     */
    type: number
    /**
     * 权重
     */
    weight: number

    /**
     * 房产 ID
     */
    property_id?: number
  }

  interface FlexiratesChangeNicknameReq {
    id: number
    nickname: string
  }

  interface FlexiratesChangePaymentMethodReq {
    id?: number
    payment_method_id: number
    payment_method_type: number
  }

  interface FlexiratesOnceOffPaymentReq {
    amount: number
    payment_method_id?: number
    property_id: number
  }

  /**
   * 银行账号，type=1 时必填
   */
  interface Bank {
    account_name: string
    /**
     * 银行账号
     */
    account_no: string
    bsb: string
    /**
     * 昵称
     */
    nickname?: string
  }

  /**
   * 信用卡，type=2 时必填
   */
  interface Card {
    /**
     * 卡号
     */
    card_number: string
    /**
     * 城市
     */
    city: string
    /**
     * 国家代码
     */
    country_iso2: string
    /**
     * 邮箱
     */
    email: string
    /**
     * 月份，MM
     */
    expiration_month: string
    /**
     * 年份，YYYY
     */
    expiration_year: string
    /**
     * 月份
     */
    // expiry_month: string
    /**
     * 年份
     */
    // expiry_year: string
    /**
     * 名
     */
    first_name: string
    /**
     * 姓
     */
    last_name: string
    /**
     * 地址1
     */
    line_1: string
    /**
     * 地址2
     */
    line_2?: string
    /**
     * 持卡名称
     */
    name_on_card: string
    /**
     * 昵称
     */
    nickname?: string
    /**
     * 电话
     */
    phone: string
    /**
     * 邮编
     */
    postcode: string
    /**
     * CVV
     */
    security_code: string
    /**
     * 州
     */
    state: string
  }

  interface PropertyDetailInfo {
    address: string
    first_payment_date: string
    id: number
    next_payment_amount: string
    /**
     * 对应Next Due日期
     */
    next_payment_date: string
    /**
     * 名称
     */
    nickname: string
    /**
     * 返回默认的主卡和副卡两张卡
     */
    payment_method: PaymentMethod[]
    schedule_details: ScheduleDetails
    status: number
    status_desc: string
    total_amount: string
    total_paid: string
    total_remaining: string
    transaction_history: TransactionHistory[]
  }

  interface PaymentMethod {
    account_name: string
    account_no: string
    bsb: string
    expiration_month: string
    expiration_year: string
    id: number
    last_payment_amount: string
    /**
     * 最后/上一次支付日期
     */
    last_payment_date: string
    /**
     * 支付类型，可选值：{
     * 1: 'Bank Account',
     * 2: 'Card Number'
     * }
     */
    type: number
    weight: number
    weight_desc: string
  }
  interface ScheduleDetails {
    /**
     * 可编辑次数
     */
    editable_count: number
    /**
     * 已编辑次数
     */
    edited_count: number
    failable_payment_count: number
    /**
     * 支付失败次数
     */
    failed_payment_count: number
    final_payment_amount: string
    payment_plan: number
    payment_plan_desc: string
    regular_payment_amount: string
  }
  interface TransactionHistory {
    payment_amount?: string
    payment_date?: string
    payment_method_desc?: string
    status?: number
    status_desc?: string
  }
}
