<script setup lang="ts">
import { Constants, Format } from '@shared'
import { toTypedSchema } from '@vee-validate/yup'
import dayjs from 'dayjs'
import { Decimal } from 'decimal.js'
import { Field, Form as VeeForm } from 'vee-validate'
import { onMounted, ref, watch } from 'vue'
import * as yup from 'yup'
import CustomDialog from '@/components/customDialog/index.vue'
import AddCardOrBank from '@/components/payment/addCardOrBank.vue'
import { usePaymentPlan } from '@/composables/usePaymentPlan'
import { home as homeApi, property as propertyApi, transactions as transactionsApi } from '@/services/flexirates'

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
  (e: 'refresh'): void
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits<Emits>()

// 验证步骤是否完成
const isVerified = ref(false)
const verifyLoading = ref(false)
const verifiedData = ref()

const showAddPaymentDialog = ref(false)
const addLoading = ref(false)

const {
  paymentPlanOptions,
  dictPaymentPlan,
  lastPaymentAmount,
  calculatedLastPaymentDate,
  maxDate,
  calculateRegularPaymentAmount,
  getPaymentAmount,
  getPaymentPlanSchedule,
} = usePaymentPlan()

const handleDialogClose = () => {
  getAllPaymentMethodList()
  showAddPaymentDialog.value = false
}

// 验证表单schema
const verifySchema = toTypedSchema(yup.object({
  propertyNumber: yup.string().required('Please enter property number.'),
  verificationCode: yup.string().required('Please enter verification code.'),
}))

// 支付表单schema
const paymentSchema = toTypedSchema(yup.object({
  primaryPayment: yup.string().required('Primary Payment Method is required'),
  secondaryPayment: yup.string().optional(),
  paymentPlanType: yup.number().min(0, 'Payment Plan Type is required'),
  paymentPlan: yup.number().when('paymentPlanType', {
    is: (val: number) => [Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS, Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT].includes(val),
    then: schema => schema.min(0, 'Payment Plan is required'),
    otherwise: schema => schema.optional(),
  }),
  firstPaymentDate: yup.date()
    .min(dayjs().startOf('day').toDate(), 'First payment date must be today or in the future')
    .typeError('First Payment Date is required')
    .required('First Payment Date is required'),
  amount: yup.string().when(['paymentPlanType'], {
    is: (paymentPlanType: number) => {
      if (paymentPlanType === Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT) {
        return true
      }
      return false
    },
    then: schema => schema.required('Amount is required').min(0, 'Amount is required'),
    otherwise: schema => schema.optional(),
  }),
}))

// 验证表单数据
const verifyModel = ref({
  propertyNumber: '',
  verificationCode: '',
})

// 支付表单数据
const paymentModel = ref({
  primaryPayment: '',
  secondaryPayment: '',
  paymentPlanType: Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS,
  paymentPlan: Constants.Flexirates.Plan.WEEKLY,
  firstPaymentDate: '',
  amount: '',
  numberOfPaymentDuring: 0,
  regularPaymentAmount: '',
  lastPaymentDate: '',
})

const methodOptions = ref()
// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
  emit('close')
  isVerified.value = false
}

// 验证提交
const onVerifySubmit = async (values: any) => {
  try {
    verifyLoading.value = true
    const res = await propertyApi.getPropertyList({
      property_number: values.propertyNumber,
      verification_code: values.verificationCode,
    })
    if (res.code === 0) {
      verifiedData.value = res.data
      isVerified.value = true
      // 设置默认的 payment plan
      if (paymentModel.value.paymentPlanType !== Constants.Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW) {
        paymentModel.value.paymentPlan = verifiedData.value?.default_payment_plan || null
      }
    }
  }
  catch (error) {
    console.error('Error verifying:', error)
  }
  finally {
    verifyLoading.value = false
  }
}

// 支付表单提交
const onPaymentSubmit = async () => {
  // Create a temporary model compatible with the composable interface
  const tempModel = {
    payment_plan: paymentModel.value.paymentPlanType,
    payment_plan_schedule: paymentModel.value.paymentPlan,
    first_payment_date: paymentModel.value.firstPaymentDate,
    amount: paymentModel.value.amount,
    regular_payment_amount: paymentModel.value.regularPaymentAmount,
    no_of_regular_payment: paymentModel.value.numberOfPaymentDuring,
  }

  const amount = getPaymentAmount(paymentModel.value.paymentPlanType, tempModel, Number(verifiedData.value?.total_amount_due) || 0)
  const payment_plan_schedule = getPaymentPlanSchedule(paymentModel.value.paymentPlanType, tempModel)

  const sendData = {
    property_number: verifyModel.value.propertyNumber,
    verification_code: verifyModel.value.verificationCode,
    primary_payment_method_id: paymentModel.value.primaryPayment,
    secondary_payment_method_id: paymentModel.value.secondaryPayment,
    payment_plan: payment_plan_schedule,
    first_payment_date: dayjs(paymentModel.value.firstPaymentDate).format('YYYY-MM-DD'),
    amount: paymentModel.value.paymentPlanType === Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT ? Number(amount) : 0,
    return_url: window.location.href,
  }
  try {
    addLoading.value = true
    const res = await homeApi.createProperty(sendData)
    if (res.code === 0) {
      handleClose()
      window.$toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Add property successful',
      })
      getAllPaymentMethodList()
      emit('refresh')
    }
  }
  catch (error) {
    console.error('Error submitting payment:', error)
  }
  finally {
    addLoading.value = false
  }
}

const handleSelectChange = (type: string, event: any) => {
  if (typeof event === 'string' && event === '') {
    showAddPaymentDialog.value = true
  }
  else {
    switch (type) {
      case 'first':
        paymentModel.value.primaryPayment = event
        break
      case 'second':
        paymentModel.value.secondaryPayment = event
        break
      default:
        break
    }
  }
}

const getAllPaymentMethodList = async () => {
  const res = await transactionsApi.getAllAccount()
  methodOptions.value = res.data.map((item) => {
    return {
      label: item.payment_method,
      value: item.id,
    }
  })
  methodOptions.value.push({ label: 'Add New Payment Method', value: '' })
}

watch([() => paymentModel.value.amount, () => paymentModel.value.firstPaymentDate, () => paymentModel.value.paymentPlan, () => paymentModel.value.paymentPlanType, () => verifiedData.value?.last_payment_date], () => {
  // Create a temporary model compatible with the composable interface
  const tempModel = {
    payment_plan: paymentModel.value.paymentPlanType,
    payment_plan_schedule: paymentModel.value.paymentPlan,
    first_payment_date: paymentModel.value.firstPaymentDate,
    amount: paymentModel.value.amount,
    regular_payment_amount: paymentModel.value.regularPaymentAmount,
    no_of_regular_payment: paymentModel.value.numberOfPaymentDuring,
  }

  calculateRegularPaymentAmount(tempModel, paymentModel.value.paymentPlanType, Number(verifiedData.value?.total_amount_due) || 0, verifiedData.value?.last_payment_date || '')

  // Update the original model with calculated values
  paymentModel.value.regularPaymentAmount = tempModel.regular_payment_amount || ''
  paymentModel.value.numberOfPaymentDuring = tempModel.no_of_regular_payment || 0
}, { immediate: true })

// 监听 paymentPlanType 变化，重置相关字段
watch(() => paymentModel.value.paymentPlanType, (newVal) => {
  if (newVal === Constants.Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW) {
    paymentModel.value.paymentPlan = Constants.Flexirates.Plan.FULL_AMOUNT
  }
  else {
    paymentModel.value.paymentPlan = verifiedData.value?.default_payment_plan || null
  }
  paymentModel.value.amount = ''
  lastPaymentAmount.value = ''
})

onMounted(() => {
  getAllPaymentMethodList()
})
</script>

<template>
  <div>
    <CustomDialog
      :visible="props.visible" :title="isVerified ? 'New Schedule' : 'Add New Property'"
      @update:visible="(val) => emit('update:visible', val)" @close="handleClose"
    >
      <template #content>
        <div class="new-schedule-container">
          <!-- 验证表单 -->
          <VeeForm
            v-if="!isVerified" class="verify-form" :validation-schema="verifySchema"
            :initial-values="verifyModel" @submit="onVerifySubmit"
          >
            <Field
              v-slot="{ field, errorMessage }" v-model="verifyModel.propertyNumber" as="div" name="propertyNumber"
              class="form-group"
            >
              <div class="flex flex-col md:flex-row md:items-center gap-2">
                <label for="propertyNumber" class="form-label">Property Number:</label>
                <InputText v-model="field.value" class="w-full" :disabled="isVerified" />
              </div>
              <Message v-if="errorMessage" severity="error" variant="simple" class="error-text verify-error">
                {{
                  errorMessage }}
              </Message>
            </Field>

            <Field
              v-slot="{ field, errorMessage }" v-model="verifyModel.verificationCode" as="div"
              name="verificationCode" class="form-group"
            >
              <div class="flex flex-col md:flex-row md:items-center gap-2">
                <label for="verificationCode" class="form-label">Verification Code:</label>
                <InputText v-model="field.value" class="w-full" :disabled="isVerified" />
              </div>
              <Message v-if="errorMessage" severity="error" variant="simple" class="error-text verify-error">
                {{ errorMessage }}
              </Message>
            </Field>

            <div class="form-actions">
              <Button
                v-if="!isVerified" type="submit" label="VERIFY" severity="warn" class="verify-btn"
                :loading="verifyLoading"
              />
              <Button v-else label="VERIFIED" severity="success" class="verify-btn" disabled />
            </div>
          </VeeForm>

          <VeeForm
            v-if="isVerified" class="payment-form" :validation-schema="paymentSchema"
            :initial-values="paymentModel" @submit="onPaymentSubmit"
          >
            <Field
              v-slot="{ field, errorMessage }" v-model="paymentModel.primaryPayment" as="div" name="primaryPayment"
              class="form-group"
            >
              <div class="flex items-center">
                <label for="primaryPayment" class="form-label payment-label">Primary Payment Method</label>
                <Select
                  v-model="field.value" :options="methodOptions" option-label="label" option-value="value"
                  class="w-full" placeholder="Select or add payment method" fluid size="large"
                  @value-change="handleSelectChange('first', $event)"
                >
                  <template #option="slotProps">
                    <div class="flex items-center">
                      <div v-if="slotProps.option.value">
                        {{ slotProps.option.label }}
                      </div>
                      <div v-else>
                        {{ slotProps.option.label }}
                      </div>
                    </div>
                  </template>
                </Select>
              </div>
              <Message v-if="errorMessage" severity="error" variant="simple" class="error-text ml-[220px]">
                {{ errorMessage }}
              </Message>
            </Field>

            <Field
              v-slot="{ field, errorMessage }" v-model="paymentModel.secondaryPayment" as="div"
              name="secondaryPayment" class="form-group"
            >
              <div class="flex items-center">
                <label for="secondaryPayment" class="form-label payment-label">Secondary Payment Method</label>
                <Select
                  v-model="field.value" :options="methodOptions" option-label="label" option-value="value"
                  class="w-full" placeholder="Select or add payment method" fluid size="large"
                  @value-change="handleSelectChange('second', $event)"
                >
                  <template #option="slotProps">
                    <div class="flex items-center">
                      <div v-if="slotProps.option.value">
                        {{ slotProps.option.label }}
                      </div>
                      <div v-else @click="showAddPaymentDialog = true">
                        {{ slotProps.option.label }}
                      </div>
                    </div>
                  </template>
                </Select>
              </div>
              <Message v-if="errorMessage" severity="error" variant="simple" class="error-text ml-[220px]">
                {{ errorMessage }}
              </Message>
              <div class="tips-text ">
                *Fallback option if the primary payment fails. Strongly recommended to avoid
                payment interruptions.
              </div>
            </Field>

            <Field
              v-slot="{ field, errorMessage, handleChange }" v-model="paymentModel.paymentPlanType" as="div"
              name="paymentPlanType" class="form-group"
            >
              <div class="flex items-center">
                <label for="paymentPlanType" class="form-label schedule-label">Payment Plan<span
                  class="required"
                >*</span>:</label>
                <Select
                  v-model="field.value" size="large" option-label="label" option-value="value"
                  :options="paymentPlanOptions" class="w-full" @value-change="handleChange"
                />
              </div>
              <Message v-if="errorMessage" severity="error" variant="simple" class="error-text ml-[160px]">
                {{ errorMessage }}
              </Message>
            </Field>

            <Field
              v-if="[Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS, Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT].includes(paymentModel.paymentPlanType)"
              v-slot="{ field, errorMessage, handleChange }" v-model="paymentModel.paymentPlan" as="div"
              name="paymentPlan" class="form-group"
            >
              <div class="flex items-center">
                <label for="paymentPlan" class="form-label schedule-label">Payment Schedule<span
                  class="required"
                >*</span>:</label>
                <Select
                  v-model="field.value" size="large" option-label="label" option-value="value"
                  :options="dictPaymentPlan" class="w-full" @value-change="handleChange"
                />
              </div>
              <Message v-if="errorMessage" severity="error" variant="simple" class="error-text ml-[160px]">
                {{ errorMessage }}
              </Message>
            </Field>

            <Field
              v-slot="{ field, errorMessage, handleChange }" v-model="paymentModel.firstPaymentDate" as="div"
              name="firstPaymentDate" class="form-group"
            >
              <div class="flex items-center">
                <label for="firstPaymentDate" class="form-label schedule-label">First Payment Date<span
                  class="required"
                >*</span>:</label>
                <div class="flex-1 flex flex-col">
                  <DatePicker
                    v-model="field.value" show-icon fluid icon-display="input" class="w-full"
                    :min-date="dayjs().add(3, 'day').toDate()" :max-date="maxDate" date-format="dd/mm/yy"
                    @value-change="handleChange"
                  />
                  <Message v-if="errorMessage" severity="error" variant="simple" class="error-text">
                    {{ errorMessage }}
                  </Message>
                </div>
              </div>
            </Field>

            <Field
              v-if="paymentModel.paymentPlanType === Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT"
              v-slot="{ field, errorMessage, handleChange }" v-model="paymentModel.amount" as="div" name="amount"
              class="form-group"
            >
              <div class="flex items-center">
                <label for="amount" class="form-label schedule-label">Amount<span class="required">*</span>:</label>
                <div class="flex flex-col flex-1">
                  <InputNumber
                    v-model="field.value" class="w-full" fluid :min-fraction-digits="2"
                    :max-fraction-digits="2" :max="Number(verifiedData?.total_amount_due || 0)"
                    @value-change="handleChange"
                  />
                  <Message v-if="errorMessage" severity="error" variant="simple" class="error-text">
                    {{ errorMessage }}
                  </Message>
                </div>
              </div>
            </Field>

            <div class="form-group font-semibold">
              <div class="flex items-center">
                <div class="form-label schedule-label">
                  Total Amount Due:
                </div>
                <div class="h-4">
                  {{ verifiedData?.total_amount_due
                    ? Format.formatAmount(new Decimal(verifiedData?.total_amount_due || 0).toFixed(2))
                    : ''
                  }}
                </div>
              </div>
              <div class="flex items-center mt-4">
                <div class="form-label schedule-label">
                  Last Payment Date:
                </div>
                <div class="h-4">
                  <template
                    v-if="paymentModel.paymentPlanType === Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT && calculatedLastPaymentDate"
                  >
                    {{ dayjs(calculatedLastPaymentDate).format('DD/MM/YYYY') }}
                  </template>
                  <template v-else>
                    {{ verifiedData?.last_payment_date ? dayjs(verifiedData?.last_payment_date).format('DD/MM/YYYY') : '' }}
                  </template>
                </div>
              </div>
              <div
                v-if="paymentModel.paymentPlanType === Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS"
                class="flex items-center mt-4 "
              >
                <div class="form-label schedule-label">
                  Regular Payment Amount:
                </div>
                <div class="h-4">
                  {{ paymentModel.regularPaymentAmount ? Format.formatAmount(new Decimal(paymentModel.regularPaymentAmount || 0).toFixed(2))
                    : '' }}
                </div>
              </div>
              <div class="flex items-center mt-4">
                <div class="form-label schedule-label">
                  Last payment Amount:
                </div>
                <div class="h-4">
                  <template v-if="paymentModel.paymentPlanType === Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT && Number(paymentModel.amount) > 0">
                    <template v-if="Number(lastPaymentAmount) > 0">
                      {{ Format.formatAmount(lastPaymentAmount) }}
                    </template>
                    <template v-else-if="Number(lastPaymentAmount) === 0">
                      {{ Format.formatAmount(new Decimal(paymentModel.amount).toFixed(2)) }}
                    </template>
                  </template>
                  <template v-else-if="paymentModel.paymentPlanType === Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS && lastPaymentAmount">
                    {{ Format.formatAmount(lastPaymentAmount) }}
                  </template>
                  <template v-else-if="paymentModel.paymentPlanType === Constants.Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW">
                    {{ Format.formatAmount(new Decimal(verifiedData?.total_amount_due || 0).toFixed(2)) }}
                  </template>
                </div>
              </div>
              <div class="flex gap-2 font-semibold mt-4">
                <div>
                  Number of payments during {{ dayjs().year() }}
                  <template v-if="verifiedData?.last_payment_date">
                    - {{ dayjs(verifiedData.last_payment_date).year() }}
                  </template>:
                </div>
                <div class="h-4">
                  <template
                    v-if="paymentModel.paymentPlanType === Constants.Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW"
                  >
                    1
                  </template>
                  <template v-else>
                    {{ paymentModel.numberOfPaymentDuring ? paymentModel.numberOfPaymentDuring : '' }}
                  </template>
                </div>
              </div>
            </div>

            <div class="important-notice">
              <p class="text-red-600 text-sm">
                Please Note that if your FlexiRates account accrues 3 dishonoured (declined) payments during the
                financial year, your FlexiRates registration will be cancelled.
              </p>
              <p class="text-red-600 text-sm">
                The regular payment amount may differ from the original setup amount if a payment is skipped or fails.
              </p>
            </div>

            <div class="form-actions">
              <Button type="submit" label="CONFIRM" severity="warn" class="confirm-btn" :loading="addLoading" />
            </div>
          </VeeForm>

          <!-- 支付表单 -->
        </div>
      </template>
    </CustomDialog>
    <AddCardOrBank v-model:visible="showAddPaymentDialog" :show-radio="true" @close="handleDialogClose" />
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/mixins/breakpoints' as *;

.new-schedule-container {
  max-height: calc(100% - 35px);
  width: 600px;

  @include media-breakpoint-down(md) {
    width: 100%;
  }

  .required {
    color: #0073cf;
    font-weight: 600;
  }

  .form-group {
    margin: 20px 0;

    .error-text {
      color: #EB001B;
      font-size: 0.875rem;
      margin-top: 0.25rem;
    }

    .verify-error {
      margin-left: 150px;
    }

    .tips-text {
      font-size: 12px;
      margin: 20px 0;
      color: #EB001B;
    }

  }

  .form-label {
    display: block;
    font-weight: 600;
    min-width: 200px;
  }

  .payment-label {
    min-width: 220px;
  }

  .schedule-label {
    min-width: 250px;
  }

  .divide {
    padding-top: 20px;
    border-bottom: 1px solid #545454;
  }

  .important-notice {
    margin: 1rem 0;

    p {
      margin-bottom: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 1.5rem;

    .verify-btn,
    .confirm-btn {
      min-width: 120px;
      --p-disabled-opacity: 1;
    }
  }

}
</style>
