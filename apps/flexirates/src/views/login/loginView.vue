<script setup lang="ts">
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import logoWhite from '@/assets/flexiratesMerchant/logo-white.png'
import logo from '@/assets/flexiratesMerchant/logo.png'
import BottomInfo from './components/bottomInfo.vue'
import ForgetPassword from './components/forgetPassword.vue'
import LoginForm from './components/loginForm.vue'
import RegisterForm from './components/registerForm.vue'

enum MobileIsShowType {
  Login = 'login',
  Register = 'register',
  ForgetPassword = 'forgetPassword',
}

document.title = import.meta.env.VITE_APP_NAME

const route = useRoute()

const forgetPasswordToken = ref<string>(route.query.forgetPasswordToken as string || '')

const mobileIsShowType = ref<MobileIsShowType>(MobileIsShowType.Login)

if (forgetPasswordToken.value) {
  mobileIsShowType.value = MobileIsShowType.ForgetPassword
}

if (route.query.type === 'register') {
  mobileIsShowType.value = MobileIsShowType.Register
}
</script>

<template>
  <div
    class="login-wrapper"
    :class="{ 'show-login': [MobileIsShowType.Login, MobileIsShowType.ForgetPassword].includes(mobileIsShowType), 'show-register': mobileIsShowType === MobileIsShowType.Register }"
  >
    <div class="logo-wrap">
      <div class="logo">
        <Image :src="mobileIsShowType === MobileIsShowType.Register ? logoWhite : logo" width="220px" alt="Image" />
      </div>
    </div>

    <div v-show="mobileIsShowType === MobileIsShowType.Login" class="login-wrap">
      <LoginForm @change-show-type="(mobileIsShowType = $event as MobileIsShowType)" />
    </div>
    <div v-if="mobileIsShowType === MobileIsShowType.ForgetPassword" class="forget-password-wrap">
      <ForgetPassword @change-show-type="(mobileIsShowType = $event as MobileIsShowType)" />
    </div>
    <div v-if="mobileIsShowType === MobileIsShowType.Register" class="register-wrap">
      <RegisterForm @change-show-type="(mobileIsShowType = $event as MobileIsShowType)" />
    </div>
    <BottomInfo />
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/mixins/breakpoints' as *;

.login-wrapper {
  position: relative;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--bg-colors-white);
  padding: 200px 0 100px;

  .logo-wrap {
    position: absolute;
    top: 54px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;

    @include media-breakpoint-down(md) {
      top: 40px;
      left: 50%;
      right: auto;
      transform: translateX(-50%);
    }
  }

  :deep(.form-tools) {
    position: absolute;
    bottom: 12px;
    left: 0;
    right: 0;
    padding: 0 50px;

    .form-tools-item {
      flex: 1;
    }
  }

  &.show-login {
    background-color: var(--bg-colors-white);

    .login-container {
      padding-top: 15vh;
      max-height: 70vh;
      height: auto;
    }

    .register-wrap {
      display: none;
    }
  }

  &.show-register {
    padding-top: 180px;
    background: #0073CF;

    :deep(.bottom-info-content) {
      color: var(--color-white);
    }

    .login-wrap,
    .forget-password-wrap {
      display: none;
    }
  }
}
</style>
