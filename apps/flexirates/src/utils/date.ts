import type { Dayjs } from 'dayjs'
import { Constants } from '@shared'
import dayjs from 'dayjs'

export const formatDate = (dateString: string | number | Date | Dayjs | null | undefined, format: string = 'MMM DD h:mm A') => {
  const defaultReturn = '-'
  if (!dateString) {
    return defaultReturn
  }
  const targetDate = dayjs(dateString)
  if (!targetDate.isValid()) {
    return defaultReturn
  }
  return targetDate.format(format)
}

export const formatSearchDate = (searchData: Record<string, any>, formatKeys: string[] | string, format: string = 'YYYY-MM-DD') => {
  const formatKey = Array.isArray(formatKeys) ? formatKeys : [formatKeys]
  const returnData = { ...searchData }
  formatKey.forEach((key) => {
    if (searchData[key]) {
      if (Array.isArray(searchData[key])) {
        returnData[key] = searchData[key].map((date: string) => dayjs(date).format(format))
      }
      else {
        returnData[key] = dayjs(searchData[key]).format(format)
      }
    }
  })
  return returnData
}

/**
 * 使用循环方法计算付款次数
 * @param firstDate 第一次付款日期
 * @param lastDate 最后付款日期
 * @param paymentSchedule 付款计划类型
 * @returns 付款次数
 */
export const calculatePaymentCountByLoop = (firstDate: Dayjs, lastDate: Dayjs, paymentSchedule: number): number => {
  let currentDate = firstDate
  let count = 0

  // 循环计算付款次数，直到超过最后付款日期
  while (currentDate.isBefore(lastDate, 'day') || currentDate.isSame(lastDate, 'day')) {
    count++

    switch (paymentSchedule) {
      case Constants.Flexirates.Plan.WEEKLY:
        currentDate = currentDate.add(7, 'day')
        break
      case Constants.Flexirates.Plan.FORTNIGHTLY:
        currentDate = currentDate.add(14, 'day')
        break
      case Constants.Flexirates.Plan.MONTHLY:
        currentDate = currentDate.add(1, 'month')
        break
      case Constants.Flexirates.Plan.QUARTERLY:
        currentDate = currentDate.add(3, 'month')
        break
      default:
        // 如果是未知的付款计划，退出循环避免无限循环
        break
    }

    // 安全检查：如果循环次数过多，退出循环
    if (count > 500) {
      console.warn('Payment count calculation exceeded maximum iterations')
      break
    }
  }

  return Math.max(count, 1)
}

/**
 * 使用循环方法计算动态最后付款日期
 * @param firstPaymentDate 第一次付款日期
 * @param paymentSchedule 付款计划类型
 * @param numberOfPayments 付款次数
 * @returns 最后付款日期 (YYYY-MM-DD 格式)
 */
export const calculateDynamicLastPaymentDate = (firstPaymentDate: Date | string, paymentSchedule: number, numberOfPayments: number): string => {
  if (!firstPaymentDate || !paymentSchedule || numberOfPayments <= 0) {
    return ''
  }

  const firstDate = dayjs(firstPaymentDate)
  let currentDate = firstDate

  // 使用循环计算最后一次付款的日期
  for (let i = 1; i < numberOfPayments; i++) {
    switch (paymentSchedule) {
      case Constants.Flexirates.Plan.WEEKLY:
        currentDate = currentDate.add(7, 'day')
        break
      case Constants.Flexirates.Plan.FORTNIGHTLY:
        currentDate = currentDate.add(14, 'day')
        break
      case Constants.Flexirates.Plan.MONTHLY:
        currentDate = currentDate.add(1, 'month')
        break
      case Constants.Flexirates.Plan.QUARTERLY:
        currentDate = currentDate.add(3, 'month')
        break
      default:
        // 如果是未知的付款计划，保持当前日期
        break
    }
  }

  return currentDate.format('YYYY-MM-DD')
}
